import { <PERSON><PERSON>, Container } from "@/components";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { showAlert } from "@/utils/alert";
import { pickFromGallery, pickMultipleFromGallery, takePhoto } from "@/utils/imagePicker";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { Modal, Pressable, ScrollView, Text, TextInput, View } from "react-native";
// Storage and type imports
import {
  MAX_IMAGE_COUNT_WITH_NORMAL_MODE,
  MAX_IMAGE_COUNT_WITH_TIMELINE_MODE,
} from "@/constants/Item";
import { Item, TimelineItem } from "@/types";
import {
  deleteDraft,
  generateId,
  getDraftById,
  getItemById,
  saveDraft,
  saveItem,
} from "@/utils/storage";

interface FormErrors {
  title?: string;
  content?: string;
  coverImage?: string;
}

export default function ItemCreateScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const params = useLocalSearchParams<{ id?: string; isDraft?: string }>();
  // Extract parameters
  const itemId = params.id;
  // Determine the mode based on parameters
  const isEditingDraft = params.isDraft === "true";
  const [editingTimelineIndex, setEditingTimelineIndex] = useState<number>(-1);

  const [item, setItem] = useState<Item>({});

  const [showTagInput, setShowTagInput] = useState(false);
  const [newTag, setNewTag] = useState("");

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [imagePickerType, setImagePickerType] = useState<"cover" | "content" | "timeline">("cover");

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFullscreenImage, setShowFullscreenImage] = useState(false);

  // Timeline fullscreen state
  const [timelineFullscreenImages, setTimelineFullscreenImages] = useState<string[]>([]);
  const [timelineCurrentImageIndex, setTimelineCurrentImageIndex] = useState(0);
  const [showTimelineFullscreenImage, setShowTimelineFullscreenImage] = useState(false);

  // Cover image fullscreen state
  const [showCoverFullscreen, setShowCoverFullscreen] = useState(false);

  // Derived state for easier access
  const title = item.title || "";
  const content = item.content || "";
  const contentMode = item.mode || "normal";
  const tags = item.tags || [];
  const timelineItems = item.timelineItems || [];
  const itemCoverImage = item.coverImage;
  const contentImages = item.images || [];

  // Load existing data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    if (!itemId) return;

    try {
      setIsLoading(true);

      if (isEditingDraft) {
        // Load draft data
        const draftResponse = await getDraftById(itemId);
        if (draftResponse.success && draftResponse.data) {
          setItem(draftResponse.data);
        } else {
          showAlert("错误", "无法加载草稿数据", undefined, { icon: "alert-circle" });
        }
      } else {
        // Load published item data for editing
        const itemResponse = await getItemById(itemId);
        if (itemResponse.success && itemResponse.data) {
          setItem(itemResponse.data);
        } else {
          showAlert("错误", "无法加载故事数据", undefined, { icon: "alert-circle" });
        }
      }
    } catch (error) {
      console.error("Error loading data:", error);
      showAlert("错误", "加载数据时出现错误", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!item.title?.trim()) {
      newErrors.title = "请输入故事标题";
    }

    if (item.mode !== "timeline" && !item.content) {
      newErrors.content = "请添加故事内容";
    }

    if (item.mode === "timeline" && (item.timelineItems || []).length <= 0) {
      newErrors.content = "请添加时间线节点";
    }

    if (!item.coverImage) {
      newErrors.coverImage = "请添加封面图片";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // TODO cy 这里不是每次退出都要保存，还是不能图省事，需要验证是否有更改才行
  const handleBack = () => {
    if (
      item.coverImage ||
      item.title ||
      item.content ||
      (item.tags || []).length > 0 ||
      (item.timelineItems || []).length > 0
    ) {
      showAlert(
        "保存草稿",
        "您有未保存的内容，是否保存为草稿？",
        [
          { text: "不保存", style: "destructive", onPress: () => router.back() },
          { text: "保存草稿", onPress: () => handleSaveDraft() },
          { text: "取消", style: "cancel" },
        ],
        { icon: "document-text" }
      );
    } else {
      router.back();
    }
  };

  const handleSaveDraft = async () => {
    try {
      setIsLoading(true);

      const response = await saveDraft({
        ...item,
        mode: item.mode || "normal",
        // 为草稿手动生成一个本地 id
        id: item.id || generateId(),
        createdAt: item.createdAt || new Date().getTime(),
        updatedAt: new Date().getTime(),
      });
      if (response.success) {
        showAlert(
          "草稿已保存",
          "您的故事草稿已成功保存！",
          [{ text: "确定", onPress: () => router.back() }],
          { icon: "checkmark-circle" }
        );
      } else {
        showAlert("保存失败", response.error || "保存草稿时出现错误，请稍后重试", undefined, {
          icon: "alert-circle",
        });
      }
    } catch (error) {
      console.error("Error saving draft:", error);
      showAlert("保存失败", "保存草稿时出现错误，请稍后重试", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      showAlert("请完善必填信息", "请检查标题和内容是否已填写", undefined, {
        icon: "information-circle",
      });
      return;
    }

    setIsLoading(true);

    const newItem: Item = {
      ...item,
      mode: item.mode || "normal",
      // TODO cy 这里是临时创建的时间，后面应为后端生成
      createdAt: item.createdAt || new Date().getTime(),
      updatedAt: new Date().getTime(),
      // TODO cy 这里是临时创建的 id，后面应为后端生成
      id: item.id || generateId(),
    };

    if (newItem.mode === "timeline") {
      newItem.content = "";
      newItem.images = [];
    } else {
      newItem.timelineItems = [];
    }

    try {
      if (isEditingDraft) {
        // delete draft
        const draftResponse = await deleteDraft(newItem.id!);
        if (!draftResponse.success) {
          throw new Error(draftResponse.error || "Failed to save draft");
        }
      }

      // Then publish the draft
      const publishResponse = await saveItem(newItem);
      if (!publishResponse.success) {
        throw new Error(publishResponse.error || "Failed to publish item");
      }

      showAlert(
        "发布成功",
        "您的故事已成功发布！",
        [{ text: "确定", onPress: () => router.back() }],
        { icon: "checkmark-circle" }
      );
    } catch (error) {
      console.error("Error publishing item:", error);
      showAlert("发布失败", error instanceof Error ? error.message : "请稍后重试", undefined, {
        icon: "alert-circle",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCoverUpload = () => {
    setImagePickerType("cover");
    setShowImagePicker(true);
  };

  const handleContentUpload = () => {
    setImagePickerType("content");
    setShowImagePicker(true);
  };

  const handleAddCoverImage = (imageUrl: string) => {
    setItem(prev => ({ ...prev, coverImage: imageUrl }));
    setShowImagePicker(false);
  };

  const handleRemoveCoverImage = () => {
    setItem(prev => ({ ...prev, coverImage: undefined }));
  };

  const handleAddContentImage = (imageUrl: string) => {
    setItem(prev => ({ ...prev, images: [...(prev.images || []), imageUrl] }));
    setShowImagePicker(false);
  };

  const handleAddMultipleContentImages = (imageUrls: string[]) => {
    const currentImages = item.images || [];
    const remainingSlots = MAX_IMAGE_COUNT_WITH_NORMAL_MODE - currentImages.length;
    const imagesToAdd = imageUrls.slice(0, remainingSlots);

    if (imagesToAdd.length < imageUrls.length) {
      showAlert(
        "提示",
        `最多只能添加${MAX_IMAGE_COUNT_WITH_NORMAL_MODE}张图片，已为您选择前${imagesToAdd.length}张`,
        undefined,
        { icon: "information-circle" }
      );
    }

    setItem(prev => ({ ...prev, images: [...currentImages, ...imagesToAdd] }));
    setShowImagePicker(false);
  };

  const handleRemoveContentImage = (index: number) => {
    setItem(prev => ({ ...prev, images: (prev.images || []).filter((_, i) => i !== index) }));
  };

  const handleAddTimelineImage = (imageUrl: string) => {
    console.log(editingTimelineIndex, "editingTimelineIndex");
    setItem(prev => ({
      ...prev,
      timelineItems: (prev.timelineItems || []).map((item, index) =>
        index === editingTimelineIndex
          ? { ...item, images: [...(item.images || []), imageUrl] }
          : item
      ),
    }));
    setShowImagePicker(false);
  };

  const handleAddMultipleTimelineImages = (imageUrls: string[]) => {
    console.log(editingTimelineIndex, "editingTimelineIndex");
    const currentItem = item.timelineItems?.[editingTimelineIndex];
    if (!currentItem) return;

    const currentImages = currentItem.images || [];
    const remainingSlots = MAX_IMAGE_COUNT_WITH_TIMELINE_MODE - currentImages.length;
    const imagesToAdd = imageUrls.slice(0, remainingSlots);

    if (imagesToAdd.length < imageUrls.length) {
      showAlert(
        "提示",
        `时间线节点最多只能添加${MAX_IMAGE_COUNT_WITH_TIMELINE_MODE}张图片，已为您选择前${imagesToAdd.length}张`,
        undefined,
        { icon: "information-circle" }
      );
    }

    setItem(prev => ({
      ...prev,
      timelineItems: (prev.timelineItems || []).map((item, index) =>
        index === editingTimelineIndex
          ? { ...item, images: [...currentImages, ...imagesToAdd] }
          : item
      ),
    }));
    setShowImagePicker(false);
  };

  const handleTakePhoto = async () => {
    try {
      const imageUrl = await takePhoto({
        allowsEditing: true,
        aspect: imagePickerType === "cover" ? [16, 9] : [1, 1],
        quality: 0.8,
      });

      if (!imageUrl) {
        // User cancelled or error occurred - close modal without showing error
        setShowImagePicker(false);
        return;
      }

      if (imagePickerType === "cover") {
        handleAddCoverImage(imageUrl);
      } else if (imagePickerType === "content") {
        handleAddContentImage(imageUrl);
      } else if (imagePickerType === "timeline") {
        handleAddTimelineImage(imageUrl);
      }
    } catch (error) {
      // Handle any unexpected errors
      console.error("Error in handleTakePhoto:", error);
      setShowImagePicker(false);
      showAlert("提示", "图片选取失败，请重试!", undefined, { icon: "image" });
    }
  };

  const handlePickFromGallery = async () => {
    try {
      // For cover images, use single selection
      if (imagePickerType === "cover") {
        const imageUrl = await pickFromGallery({
          allowsEditing: true,
          aspect: [16, 9],
          quality: 0.8,
        });

        if (!imageUrl) {
          // User cancelled or error occurred - close modal without showing error
          setShowImagePicker(false);
          return;
        }

        handleAddCoverImage(imageUrl);
        return;
      }

      // For content and timeline images, use multiple selection
      const imageUrls = await pickMultipleFromGallery({
        quality: 0.8,
        selectionLimit:
          imagePickerType === "content"
            ? MAX_IMAGE_COUNT_WITH_NORMAL_MODE
            : MAX_IMAGE_COUNT_WITH_TIMELINE_MODE,
      });

      if (imageUrls.length === 0) {
        // User cancelled or error occurred - close modal without showing error
        setShowImagePicker(false);
        return;
      }

      if (imagePickerType === "content") {
        handleAddMultipleContentImages(imageUrls);
      } else if (imagePickerType === "timeline") {
        handleAddMultipleTimelineImages(imageUrls);
      }
    } catch (error) {
      // Handle any unexpected errors
      console.error("Error in handlePickFromGallery:", error);
      setShowImagePicker(false);
      showAlert("提示", "图片选取失败，请重试!", undefined, { icon: "image" });
    }
  };

  // Removed handleImagePress as it's now handled inline

  const handleTimelineImagePress = (timelineItem: TimelineItem, imageIndex: number) => {
    setTimelineFullscreenImages(timelineItem.images || []);
    setTimelineCurrentImageIndex(imageIndex);
    setShowTimelineFullscreenImage(true);
  };

  const handleCoverImagePress = () => {
    setShowCoverFullscreen(true);
  };

  const handleAddTag = () => {
    setShowTagInput(true);
  };

  const handleSaveTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setItem(prev => ({ ...prev, tags: [...(prev.tags || []), newTag.trim()] }));
      setNewTag("");
      setShowTagInput(false);
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setItem(prev => ({ ...prev, tags: (prev.tags || []).filter(tag => tag !== tagToRemove) }));
  };

  const handleAddTimelineItem = () => {
    if (item.timelineItems?.length) {
      const emptyTimeline = item.timelineItems.findIndex(item => item.content.trim() === "");

      if (emptyTimeline >= 0) {
        showAlert("提示", "请先完成之前的内容填写", undefined, { icon: "information-circle" });
        return;
      }
    }

    setItem(prev => ({
      ...prev,
      timelineItems: [
        ...(prev.timelineItems || []),
        {
          title: "",
          content: "",
          images: [],
        },
      ],
    }));
    setEditingTimelineIndex(item.timelineItems?.length || 0);
  };

  const handleEditTimelineItem = (updatedItem: TimelineItem, index: number) => {
    setItem(prev => ({
      ...prev,
      timelineItems: (prev.timelineItems || []).map((item, idx) =>
        idx === index ? updatedItem : item
      ),
    }));
  };

  const handleRemoveTimelineItem = (index: number) => {
    setItem(prev => ({
      ...prev,
      timelineItems: (prev.timelineItems || []).filter((_, idx) => idx !== index),
    }));
    setEditingTimelineIndex(-1);
  };

  const handleAddImageWithTimeline = () => {
    setImagePickerType("timeline");
    setShowImagePicker(true);
  };

  const handleConfirmEditTimelineItem = () => {
    const newItem = item.timelineItems?.slice(-1)![0];
    if (!newItem?.content) {
      showAlert("提示", "请填写内容", undefined, { icon: "information-circle" });
      return;
    }

    setEditingTimelineIndex(-1);
  };

  return (
    <>
      <Container
        style={{ flex: 1, backgroundColor: colors.backgroundApp }}
        headerProps={{
          title: itemId ? "编辑故事" : "创建新故事",
          leftAction: { icon: "arrow-back", onPress: handleBack },
          rightActions: [
            {
              icon: isLoading ? "hourglass" : "checkmark",
              onPress: isLoading ? () => {} : handleSave,
            },
          ],
        }}
        enableScroll
      >
        <View style={[styles.content, { backgroundColor: colors.backgroundApp }]}>
          {/* Cover Images */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionLabel, { color: colors.text }]}>
                故事封面 <Text style={[styles.required, { color: colors.error }]}>*</Text>
              </Text>
              {!errors.coverImage && (
                <Text style={[styles.errorText, { color: colors.error }]}>{errors.coverImage}</Text>
              )}
            </View>

            {itemCoverImage ? (
              <View style={styles.heroImageContainer}>
                <Pressable onPress={handleCoverImagePress}>
                  <Image
                    source={{ uri: itemCoverImage }}
                    style={styles.heroImage}
                    contentFit="cover"
                  />
                </Pressable>
                <View style={styles.heroImageOverlay}>
                  <Pressable
                    style={[styles.changeImageButton, { backgroundColor: "rgba(0, 0, 0, 0.6)" }]}
                    onPress={handleCoverUpload}
                  >
                    <Ionicons name="camera" size={20} color="white" />
                    <Text style={styles.changeImageText}>更换封面</Text>
                  </Pressable>
                  <Pressable
                    style={[styles.removeImageButton, { backgroundColor: colors.error }]}
                    onPress={() => handleRemoveCoverImage()}
                  >
                    <Ionicons name="close" size={16} color="white" />
                  </Pressable>
                </View>
              </View>
            ) : (
              <Pressable
                style={[
                  styles.coverUploadLarge,
                  {
                    borderColor: errors.coverImage ? colors.error : "#e5e7eb",
                  },
                ]}
                onPress={handleCoverUpload}
              >
                <View style={styles.uploadIconContainer}>
                  <Ionicons name="camera" size={48} color={colors.primary} />
                </View>
                <Text style={[styles.coverUploadTitle, { color: colors.text }]}>添加故事封面</Text>
                <Text style={[styles.coverUploadSubtitle, { color: colors.textMuted }]}>
                  选择一张最能代表这个故事的图片
                </Text>
                <Text style={[styles.coverUploadHint, { color: colors.textMuted }]}>
                  建议尺寸 16:9，支持 JPG、PNG 格式
                </Text>
              </Pressable>
            )}
          </View>

          {/* item Title */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>
              故事标题 <Text style={[styles.required, { color: colors.error }]}>*</Text>
            </Text>
            <TextInput
              style={[
                styles.titleInput,
                {
                  color: colors.text,
                  borderColor: errors.title ? colors.error : colors.border,
                  borderWidth: errors.title ? 2 : 1,
                },
              ]}
              value={title}
              onChangeText={text => {
                setItem(prev => ({ ...prev, title: text }));
                if (errors.title) {
                  setErrors(prev => ({ ...prev, title: undefined }));
                }
              }}
              placeholder="请输入故事标题"
              placeholderTextColor={colors.textMuted}
              maxLength={50}
            />
            {errors.title && (
              <Text style={[styles.errorText, { color: colors.error }]}>{errors.title}</Text>
            )}
            <Text style={[styles.characterCount, { color: colors.textMuted }]}>
              {title.length}/50
            </Text>
          </View>

          {/* Content Mode Selector */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>内容模式</Text>
            <View style={[styles.modeSelector, { backgroundColor: colors.backgroundTertiary }]}>
              <Pressable
                style={[
                  styles.modeOption,
                  contentMode === "normal" && { backgroundColor: colors.primary },
                ]}
                onPress={() => setItem(prev => ({ ...prev, mode: "normal" }))}
              >
                <Ionicons
                  name="document-text"
                  size={20}
                  color={contentMode === "normal" ? "white" : colors.textMuted}
                />
                <Text
                  style={[
                    styles.modeOptionText,
                    { color: contentMode === "normal" ? "white" : colors.textMuted },
                  ]}
                >
                  普通模式
                </Text>
              </Pressable>

              <Pressable
                style={[
                  styles.modeOption,
                  contentMode === "timeline" && { backgroundColor: colors.primary },
                ]}
                onPress={() => setItem(prev => ({ ...prev, mode: "timeline" }))}
              >
                <Ionicons
                  name="time"
                  size={20}
                  color={contentMode === "timeline" ? "white" : colors.textMuted}
                />
                <Text
                  style={[
                    styles.modeOptionText,
                    { color: contentMode === "timeline" ? "white" : colors.textMuted },
                  ]}
                >
                  时间线模式
                </Text>
              </Pressable>
            </View>
          </View>

          {/* Item Content */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            {contentMode === "normal" ? (
              <>
                <Text style={[styles.sectionLabel, { color: colors.text }]}>
                  物品故事 <Text style={[styles.required, { color: colors.error }]}>*</Text>
                </Text>
                <TextInput
                  style={[
                    styles.contentInput,
                    {
                      color: colors.text,
                      borderColor: errors.content ? colors.error : colors.border,
                      borderWidth: errors.content ? 2 : 1,
                    },
                  ]}
                  value={content}
                  onChangeText={text => {
                    setItem(prev => ({ ...prev, content: text }));
                    if (errors.content) {
                      setErrors(prev => ({ ...prev, content: undefined }));
                    }
                  }}
                  placeholder="讲述这个物品的故事...&#10;&#10;您可以描述：&#10;• 物品的来历和背景&#10;• 与物品相关的回忆&#10;• 物品的特殊意义&#10;• 想要传达的情感"
                  placeholderTextColor={colors.textMuted}
                  multiline
                  numberOfLines={8}
                  textAlignVertical="top"
                  maxLength={2000}
                />
                {errors.content && (
                  <Text style={[styles.errorText, { color: colors.error }]}>{errors.content}</Text>
                )}
                <Text style={[styles.characterCount, { color: colors.textMuted }]}>
                  {content.length}/2000
                </Text>

                {/* Content Images for Normal Mode */}
                <View style={styles.contentImagesSection}>
                  <View style={styles.sectionHeader}>
                    <Text style={[styles.sectionLabel, { color: colors.text }]}>物品图片</Text>
                    {contentImages.length < MAX_IMAGE_COUNT_WITH_NORMAL_MODE && (
                      <Button
                        title="添加图片"
                        variant="ghost"
                        size="small"
                        icon={<Ionicons name="camera" size={16} color={colors.primary} />}
                        onPress={handleContentUpload}
                        style={{ paddingHorizontal: 0 }}
                      />
                    )}
                  </View>

                  {contentImages.length > 0 ? (
                    <View style={styles.contentImagesContainer}>
                      <ScrollView
                        horizontal
                        pagingEnabled
                        showsHorizontalScrollIndicator={false}
                        onMomentumScrollEnd={event => {
                          const index = Math.round(
                            event.nativeEvent.contentOffset.x /
                              event.nativeEvent.layoutMeasurement.width
                          );
                          setCurrentImageIndex(index);
                        }}
                        style={styles.contentImagesScrollView}
                      >
                        {contentImages.map((imageUrl, index) => (
                          <Pressable
                            key={index}
                            style={styles.contentImageSlide}
                            onPress={() => {
                              setCurrentImageIndex(index);
                              setShowFullscreenImage(true);
                            }}
                          >
                            <Image source={{ uri: imageUrl }} style={styles.contentImageDisplay} />
                            <Pressable
                              style={[
                                styles.removeContentImageButton,
                                { backgroundColor: colors.error },
                              ]}
                              onPress={() => handleRemoveContentImage(index)}
                            >
                              <Ionicons name="close" size={16} color="white" />
                            </Pressable>
                          </Pressable>
                        ))}
                      </ScrollView>

                      {/* Image Indicators */}
                      <View style={styles.contentImageIndicators}>
                        {contentImages.map((_, index) => (
                          <View
                            key={index}
                            style={[
                              styles.contentImageIndicator,
                              {
                                backgroundColor:
                                  index === currentImageIndex ? colors.primary : colors.border,
                                opacity: index === currentImageIndex ? 1 : 0.5,
                              },
                            ]}
                          />
                        ))}
                      </View>

                      <Text style={[styles.imageCountText, { color: colors.textMuted }]}>
                        {contentImages.length} 张图片
                      </Text>
                    </View>
                  ) : (
                    <View
                      style={[
                        styles.emptyContentImages,
                        { backgroundColor: colors.backgroundTertiary },
                      ]}
                    >
                      <Ionicons name="images-outline" size={48} color={colors.textMuted} />
                      <Text style={[styles.emptyContentImagesText, { color: colors.textMuted }]}>
                        还没有添加图片
                      </Text>
                      <Text style={[styles.emptyContentImagesHint, { color: colors.textMuted }]}>
                        点击&ldquo;添加图片&rdquo;来丰富故事内容
                      </Text>
                    </View>
                  )}
                </View>
              </>
            ) : (
              <>
                <View style={styles.timelineHeader}>
                  <Text style={[styles.sectionLabel, { color: colors.text }]}>
                    时间线内容 <Text style={[styles.required, { color: colors.error }]}>*</Text>
                  </Text>
                  <Button
                    title="添加节点"
                    variant="ghost"
                    size="small"
                    icon={<Ionicons name="add" size={16} color={colors.primary} />}
                    onPress={handleAddTimelineItem}
                  />
                </View>

                {timelineItems.length > 0 ? (
                  <View style={styles.timelineContainer}>
                    {timelineItems.map((item, index) => (
                      <View key={index} style={styles.timelineNode}>
                        <View style={styles.timelineNodeHeader}>
                          <View
                            style={[styles.timelineNodeDot, { backgroundColor: colors.primary }]}
                          />
                          <View
                            style={[styles.timelineNodeLine, { backgroundColor: colors.border }]}
                          />
                        </View>

                        <View style={styles.timelineNodeContent}>
                          <View
                            style={[
                              styles.timelineNodeCard,
                              { backgroundColor: colors.background },
                            ]}
                          >
                            {editingTimelineIndex === index ? (
                              <>
                                <TextInput
                                  style={[
                                    styles.timelineNodeTitle,
                                    { color: colors.text, borderColor: colors.border },
                                  ]}
                                  value={item.title}
                                  onChangeText={text =>
                                    handleEditTimelineItem({ ...item, title: text }, index)
                                  }
                                  placeholder="时间节点标题(选填)"
                                  placeholderTextColor={colors.textMuted}
                                  maxLength={50}
                                />

                                <TextInput
                                  style={[
                                    styles.timelineNodeInput,
                                    { color: colors.text, borderColor: colors.border },
                                  ]}
                                  value={item.content}
                                  onChangeText={text =>
                                    handleEditTimelineItem({ ...item, content: text }, index)
                                  }
                                  placeholder="描述这个时间点发生的事情..."
                                  placeholderTextColor={colors.textMuted}
                                  multiline
                                  numberOfLines={4}
                                  textAlignVertical="top"
                                  maxLength={500}
                                />
                              </>
                            ) : (
                              <>
                                <Text
                                  style={[styles.timelineNodeTitleView, { color: colors.text }]}
                                >
                                  {item.title || "未命名节点"}
                                </Text>
                                <Text
                                  style={[
                                    styles.timelineNodeContentView,
                                    { color: colors.textSecondary },
                                  ]}
                                >
                                  {item.content || "暂无内容"}
                                </Text>
                              </>
                            )}

                            {/* Timeline Node Images */}
                            {(item.images?.length || editingTimelineIndex === index) && (
                              <View style={styles.timelineImagesContainer}>
                                <ScrollView
                                  horizontal
                                  showsHorizontalScrollIndicator={false}
                                  style={styles.timelineImagesScrollView}
                                >
                                  <View style={styles.timelineImagesRow}>
                                    {/* Existing Images */}
                                    {item.images?.map((imageUrl, imgIndex) => (
                                      <View key={imgIndex} style={styles.timelineImageItem}>
                                        <Pressable
                                          onPress={() => handleTimelineImagePress(item, imgIndex)}
                                        >
                                          <Image
                                            source={{ uri: imageUrl }}
                                            style={styles.timelineNodeImage}
                                          />
                                        </Pressable>
                                        {editingTimelineIndex === index && (
                                          <Pressable
                                            style={[
                                              styles.removeTimelineImageButton,
                                              { backgroundColor: colors.error },
                                            ]}
                                            onPress={() => {
                                              const newImages =
                                                item.images?.filter((_, i) => i !== imgIndex) || [];
                                              handleEditTimelineItem(
                                                { ...item, images: newImages },
                                                index
                                              );
                                            }}
                                          >
                                            <Ionicons name="close" size={12} color="white" />
                                          </Pressable>
                                        )}
                                      </View>
                                    ))}

                                    {/* Add Image Button */}
                                    {item.images!.length < MAX_IMAGE_COUNT_WITH_TIMELINE_MODE && (
                                      <Pressable
                                        style={[
                                          styles.addImageButton,
                                          {
                                            backgroundColor: colors.backgroundTertiary,
                                            borderColor: colors.border,
                                          },
                                        ]}
                                        onPress={handleAddImageWithTimeline}
                                      >
                                        <Ionicons name="add" size={24} color={colors.primary} />
                                      </Pressable>
                                    )}
                                  </View>
                                </ScrollView>

                                {item.images && item.images.length > 0 && (
                                  <Text
                                    style={[styles.imageCountText, { color: colors.textMuted }]}
                                  >
                                    {item.images.length} 张图片
                                  </Text>
                                )}
                              </View>
                            )}

                            {/* Timeline Node Actions */}
                            {editingTimelineIndex === index ? (
                              <View style={styles.timelineNodeActions}>
                                <Pressable
                                  style={[
                                    styles.timelineNodeAction,
                                    { backgroundColor: colors.success },
                                  ]}
                                  onPress={handleConfirmEditTimelineItem}
                                >
                                  <Ionicons name="checkmark" size={16} color="white" />
                                  <Text style={[styles.timelineNodeActionText, { color: "white" }]}>
                                    确认
                                  </Text>
                                </Pressable>
                                <Pressable
                                  style={[
                                    styles.timelineNodeAction,
                                    { backgroundColor: colors.error },
                                  ]}
                                  onPress={() => handleRemoveTimelineItem(index)}
                                >
                                  <Ionicons name="trash" size={16} color="white" />
                                  <Text style={[styles.timelineNodeActionText, { color: "white" }]}>
                                    删除
                                  </Text>
                                </Pressable>
                              </View>
                            ) : (
                              <View style={styles.timelineNodeViewActions}>
                                <Pressable
                                  style={[
                                    styles.timelineNodeEditButton,
                                    { backgroundColor: colors.backgroundTertiary },
                                  ]}
                                  onPress={() => setEditingTimelineIndex(index)}
                                >
                                  <Ionicons name="create" size={16} color={colors.primary} />
                                  <Text
                                    style={[
                                      styles.timelineNodeActionText,
                                      { color: colors.primary },
                                    ]}
                                  >
                                    编辑
                                  </Text>
                                </Pressable>
                              </View>
                            )}
                          </View>
                        </View>
                      </View>
                    ))}
                  </View>
                ) : (
                  <View
                    style={[styles.emptyTimeline, { backgroundColor: colors.backgroundTertiary }]}
                  >
                    <Ionicons name="time-outline" size={48} color={colors.textMuted} />
                    <Text style={[styles.emptyTimelineText, { color: colors.textMuted }]}>
                      还没有时间线内容
                    </Text>
                    <Text style={[styles.emptyTimelineHint, { color: colors.textMuted }]}>
                      点击&ldquo;添加节点&rdquo;开始创建时间线
                    </Text>
                  </View>
                )}
              </>
            )}
          </View>

          {/* Tags */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>标签</Text>

            <View style={styles.tagsContainer}>
              {tags.map((tag, index) => (
                <View key={index} style={styles.customTagChip}>
                  <Text style={[styles.customTagText, { color: colors.primary }]}>{tag}</Text>
                  <Pressable
                    onPress={() => handleRemoveTag(tag)}
                    style={[styles.customTagRemove, { backgroundColor: colors.primary }]}
                  >
                    <Ionicons name="close" size={12} color="white" />
                  </Pressable>
                </View>
              ))}

              {showTagInput ? (
                <View style={styles.tagInputContainer}>
                  <TextInput
                    style={[styles.tagInput, { color: colors.text, borderColor: colors.primary }]}
                    value={newTag}
                    onChangeText={setNewTag}
                    placeholder="输入标签"
                    placeholderTextColor={colors.textMuted}
                    autoFocus
                    onSubmitEditing={handleSaveTag}
                    maxLength={10}
                  />
                  <Pressable
                    onPress={handleSaveTag}
                    style={[styles.tagSaveButton, { backgroundColor: colors.primary }]}
                  >
                    <Ionicons name="checkmark" size={16} color="white" />
                  </Pressable>
                  <Pressable
                    onPress={() => setShowTagInput(false)}
                    style={[styles.tagCancelButton, { backgroundColor: colors.textMuted }]}
                  >
                    <Ionicons name="close" size={16} color="white" />
                  </Pressable>
                </View>
              ) : (
                <Pressable
                  onPress={handleAddTag}
                  style={[
                    styles.addTagButton,
                    { backgroundColor: colors.backgroundTertiary, borderColor: colors.border },
                  ]}
                >
                  <Ionicons name="add" size={16} color={colors.primary} />
                  <Text style={[styles.addTagText, { color: colors.primary }]}>添加标签</Text>
                </Pressable>
              )}
            </View>

            {/* Suggested Tags */}
            {/* // TODO cy 推荐标签 */}
            <View style={styles.suggestedTags}>
              <Text style={[styles.suggestedTagsLabel, { color: colors.textMuted }]}>
                推荐标签：
              </Text>
              <View style={styles.suggestedTagsContainer}>
                {["家族记忆", "老物件", "收藏品", "纪念品", "传承", "回忆"].map(suggestedTag => (
                  <Pressable
                    key={suggestedTag}
                    style={[
                      styles.suggestedTag,
                      { backgroundColor: colors.backgroundTertiary, borderColor: colors.border },
                    ]}
                    onPress={() =>
                      setItem(prev => ({ ...prev, tags: [...(prev.tags || []), suggestedTag] }))
                    }
                  >
                    <Text style={[styles.suggestedTagText, { color: colors.textSecondary }]}>
                      {suggestedTag}
                    </Text>
                  </Pressable>
                ))}
              </View>
            </View>
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Button
              title="保存草稿"
              variant="outline"
              onPress={handleSaveDraft}
              style={styles.actionButton}
              icon={<Ionicons name="document-text" size={16} color={colors.primary} />}
              disabled={isLoading}
            />
            <Button
              title={isLoading ? "发布中..." : "发布故事"}
              variant="primary"
              onPress={handleSave}
              style={styles.actionButton}
              icon={
                isLoading ? (
                  <Ionicons name="hourglass" size={16} color="white" />
                ) : (
                  <Ionicons name="send" size={16} color="white" />
                )
              }
              disabled={isLoading}
            />
          </View>
        </View>
      </Container>

      {/* Image Picker Modal */}
      <Modal
        visible={showImagePicker}
        transparent
        animationType="slide"
        onRequestClose={() => setShowImagePicker(false)}
      >
        <Pressable style={styles.modalOverlay} onPress={() => setShowImagePicker(false)}>
          <Pressable
            style={[styles.imagePickerModal, { backgroundColor: colors.background }]}
            onPress={e => e.stopPropagation()}
          >
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {imagePickerType === "cover" ? "选择封面图片" : "选择图片"}
              </Text>
              <Pressable onPress={() => setShowImagePicker(false)}>
                <Ionicons name="close" size={24} color={colors.textMuted} />
              </Pressable>
            </View>

            {imagePickerType !== "cover" && (
              <Text style={[styles.multiSelectHint, { color: colors.textMuted }]}>
                可以选择多张图片，最多
                {imagePickerType === "content"
                  ? MAX_IMAGE_COUNT_WITH_NORMAL_MODE
                  : MAX_IMAGE_COUNT_WITH_TIMELINE_MODE}
                张
              </Text>
            )}

            <View style={styles.imagePickerOptions}>
              <Pressable
                style={[styles.imagePickerOption, { backgroundColor: colors.backgroundTertiary }]}
                onPress={handleTakePhoto}
              >
                <Ionicons name="camera" size={32} color={colors.primary} />
                <Text style={[styles.imagePickerOptionText, { color: colors.text }]}>拍照</Text>
                <Text style={[styles.imagePickerOptionSubtext, { color: colors.textMuted }]}>
                  单张拍摄
                </Text>
              </Pressable>

              <Pressable
                style={[styles.imagePickerOption, { backgroundColor: colors.backgroundTertiary }]}
                onPress={handlePickFromGallery}
              >
                <Ionicons name="images" size={32} color={colors.primary} />
                <Text style={[styles.imagePickerOptionText, { color: colors.text }]}>相册</Text>
                <Text style={[styles.imagePickerOptionSubtext, { color: colors.textMuted }]}>
                  {imagePickerType === "cover" ? "单张选择" : "多张选择"}
                </Text>
              </Pressable>
            </View>
          </Pressable>
        </Pressable>
      </Modal>

      {/* Fullscreen Image Modal for Normal Mode */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showFullscreenImage,
        images: contentImages,
        currentIndex: currentImageIndex,
        onClose: () => setShowFullscreenImage(false),
        onIndexChange: setCurrentImageIndex,
      })}

      {/* Fullscreen Image Modal for Timeline Mode */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showTimelineFullscreenImage,
        images: timelineFullscreenImages,
        currentIndex: timelineCurrentImageIndex,
        onClose: () => setShowTimelineFullscreenImage(false),
        onIndexChange: setTimelineCurrentImageIndex,
      })}

      {/* Fullscreen Image Modal for Cover Image */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showCoverFullscreen,
        images: itemCoverImage ? [itemCoverImage] : [],
        currentIndex: 0,
        onClose: () => setShowCoverFullscreen(false),
        onIndexChange: () => {}, // No index change needed for single image
      })}
    </>
  );
}

const styles = StyleSheetCreate({
  content: {
    flex: 1,
    paddingHorizontal: Layout.spacing.base,
  },
  section: {
    marginBottom: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
  },
  sectionLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Layout.spacing.sm,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.sm,
  },

  // Form validation
  required: {
    fontSize: Typography.fontSize.sm,
  },
  errorText: {
    fontSize: Typography.fontSize.xs,
    marginTop: Layout.spacing.xs,
  },
  characterCount: {
    fontSize: Typography.fontSize.xs,
    textAlign: "right",
    marginTop: Layout.spacing.xs,
  },

  // Hero Cover Image
  heroImageContainer: {
    height: 280,
    borderRadius: Layout.borderRadius.xl,
    overflow: "hidden",
    position: "relative",
    marginBottom: Layout.spacing.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  heroImage: {
    width: "100%",
    height: "100%",
  },
  heroImageOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "space-between",
    alignItems: "flex-end",
    padding: Layout.spacing.base,
    flexDirection: "row",
  },
  changeImageButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    gap: Layout.spacing.xs,
  },
  changeImageText: {
    color: "white",
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  removeImageButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },

  // Large Cover Upload
  coverUploadLarge: {
    height: 280,
    borderRadius: Layout.borderRadius.xl,
    justifyContent: "center",
    alignItems: "center",
    padding: Layout.spacing.xl,
    borderWidth: 2,
    borderStyle: "dashed",
  },
  uploadIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(59, 130, 246, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: Layout.spacing.lg,
  },
  coverUploadTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Layout.spacing.sm,
    textAlign: "center",
  },
  coverUploadSubtitle: {
    fontSize: Typography.fontSize.base,
    marginBottom: Layout.spacing.base,
    textAlign: "center",
    lineHeight: 22,
  },
  coverUploadHint: {
    fontSize: Typography.fontSize.sm,
    textAlign: "center",
    lineHeight: 18,
  },

  // Form inputs
  titleInput: {
    borderWidth: 2,
    borderRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.lg,
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  contentInput: {
    borderWidth: 2,
    borderRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.lg,
    fontSize: Typography.fontSize.base,
    lineHeight: 24,
    minHeight: 220,
    textAlignVertical: "top",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },

  // Timeline
  emptyTimeline: {
    padding: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.lg,
    alignItems: "center",
  },
  emptyTimelineText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginTop: Layout.spacing.sm,
  },
  emptyTimelineHint: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
    textAlign: "center",
  },

  // Tags
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
    marginBottom: Layout.spacing.sm,
  },
  customTagChip: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#ffffff",
    borderRadius: Layout.borderRadius.xl,
    paddingLeft: Layout.spacing.base,
    paddingRight: Layout.spacing.xs,
    paddingVertical: Layout.spacing.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
    borderWidth: 2,
    borderColor: "#e5e7eb",
  },
  customTagText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    marginRight: Layout.spacing.xs,
  },
  customTagRemove: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  addTagButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.xl,
    borderWidth: 2,
    borderStyle: "dashed",
    gap: Layout.spacing.xs,
  },
  addTagText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  tagInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: Layout.spacing.xs,
  },
  tagInput: {
    borderWidth: 2,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.sm,
    minWidth: 120,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  tagSaveButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  tagCancelButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  suggestedTags: {
    marginTop: Layout.spacing.sm,
  },
  suggestedTagsLabel: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Layout.spacing.sm,
  },
  suggestedTagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.xs,
  },
  suggestedTag: {
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    borderWidth: 1,
    backgroundColor: "#ffffff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  suggestedTagText: {
    fontSize: Typography.fontSize.sm,
  },

  // Action buttons
  actionButtons: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
    marginTop: Layout.spacing.lg,
    marginBottom: Layout.spacing.xl,
  },
  actionButton: {
    flex: 1,
  },

  addButtonText: {
    fontSize: Typography.fontSize.sm,
  },

  // Content Mode Selector
  modeSelector: {
    flexDirection: "row",
    borderRadius: Layout.borderRadius.xl,
    padding: 4,
    backgroundColor: "#f8f9fa",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  modeOption: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: Layout.spacing.base,
    paddingHorizontal: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    marginHorizontal: 2,
    gap: Layout.spacing.xs,
  },
  modeOptionText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
  },

  // Timeline Styles
  timelineHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.base,
  },
  timelineContainer: {
    gap: Layout.spacing.lg,
  },
  timelineNode: {
    flexDirection: "row",
    gap: Layout.spacing.base,
  },
  timelineNodeHeader: {
    alignItems: "center",
    paddingTop: Layout.spacing.sm,
  },
  timelineNodeDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 3,
    borderColor: "white",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  timelineNodeLine: {
    width: 3,
    height: 60,
    marginTop: Layout.spacing.sm,
  },
  timelineNodeContent: {
    flex: 1,
  },
  timelineNodeCard: {
    borderRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.lg,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    gap: Layout.spacing.sm,
  },
  timelineNodeTitle: {
    borderWidth: 2,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  timelineNodeInput: {
    borderWidth: 2,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.sm,
    lineHeight: 20,
    minHeight: 100,
    textAlignVertical: "top",
  },
  timelineNodeImageContainer: {
    position: "relative",
    alignSelf: "flex-start",
  },
  timelineImagesContainer: {
    marginTop: Layout.spacing.sm,
  },
  timelineImagesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.xs,
    marginBottom: Layout.spacing.xs,
  },
  timelineImageItem: {
    position: "relative",
  },
  timelineNodeImage: {
    width: 80,
    height: 80,
    borderRadius: Layout.borderRadius.lg,
  },
  removeTimelineImageButton: {
    position: "absolute",
    top: 0,
    right: -6,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  imageCountText: {
    fontSize: Typography.fontSize.xs,
    marginTop: Layout.spacing.xs,
  },
  timelineNodeActions: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
    marginTop: Layout.spacing.sm,
  },
  timelineNodeAction: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    gap: Layout.spacing.xs,
  },
  timelineNodeActionText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  timelineImagesScrollView: {
    maxHeight: 90,
  },
  timelineImagesRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: Layout.spacing.md,
    paddingRight: Layout.spacing.base,
  },
  addImageButton: {
    width: 80,
    height: 80,
    borderRadius: Layout.borderRadius.lg,
    borderWidth: 2,
    borderStyle: "dashed",
    justifyContent: "center",
    alignItems: "center",
  },
  timelineNodeViewActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: Layout.spacing.sm,
  },
  timelineNodeEditButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    gap: Layout.spacing.xs,
  },
  timelineNodeTitleView: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Layout.spacing.sm,
  },
  timelineNodeContentView: {
    fontSize: Typography.fontSize.base,
    lineHeight: 22,
    marginBottom: Layout.spacing.sm,
  },

  // Modals
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  imagePickerModal: {
    borderTopLeftRadius: Layout.borderRadius.xl,
    borderTopRightRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.xl,
    minHeight: 240,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.lg,
  },
  modalTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
  },
  imagePickerOptions: {
    flexDirection: "row",
    gap: Layout.spacing.lg,
    justifyContent: "center",
  },
  imagePickerOption: {
    padding: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.xl,
    alignItems: "center",
    minWidth: 120,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
  },
  imagePickerOptionText: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  imagePickerOptionSubtext: {
    fontSize: Typography.fontSize.xs,
    marginTop: Layout.spacing.xs,
    textAlign: "center",
  },
  multiSelectHint: {
    fontSize: Typography.fontSize.sm,
    textAlign: "center",
    marginBottom: Layout.spacing.lg,
    paddingHorizontal: Layout.spacing.lg,
  },

  // Content Images for Normal Mode
  contentImagesSection: {
    marginTop: Layout.spacing.lg,
  },
  contentImagesContainer: {
    // marginTop: Layout.spacing.sm,
  },
  contentImagesScrollView: {
    height: 200,
  },
  contentImageSlide: {
    width: 300,
    height: 200,
    marginRight: Layout.spacing.sm,
    position: "relative",
  },
  contentImageDisplay: {
    width: "100%",
    height: "100%",
    borderRadius: Layout.borderRadius.lg,
  },
  removeContentImageButton: {
    position: "absolute",
    top: 8,
    right: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  contentImageIndicators: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: Layout.spacing.sm,
    gap: Layout.spacing.xs,
  },
  contentImageIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  emptyContentImages: {
    padding: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.lg,
    alignItems: "center",
    marginTop: Layout.spacing.sm,
  },
  emptyContentImagesText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginTop: Layout.spacing.sm,
  },
  emptyContentImagesHint: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
    textAlign: "center",
  },
});
