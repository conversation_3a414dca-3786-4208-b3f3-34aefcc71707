import { Container } from "@/components";
import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { showAlert } from "@/utils/alert";
import { pickMultipleFromGallery, takePhoto } from "@/utils/imagePicker";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { Modal, Pressable, ScrollView, Text, TextInput, View } from "react-native";
// Storage and type imports
import { MAX_IMAGE_COUNT } from "@/constants/Item";
import { Item } from "@/types";
import {
    deleteDraft,
    generateId,
    getDraftById,
    getItemById,
    saveDraft,
    saveItem,
} from "@/utils/storage";

interface FormErrors {
  name?: string;
  content?: string;
  currentLocation?: string;
  timeOfPossession?: string;
}

export default function ItemCreateScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const params = useLocalSearchParams<{ id?: string; isDraft?: string }>();
  // Extract parameters
  const itemId = params.id;
  // Determine the mode based on parameters
  const isEditingDraft = params.isDraft === "true";

  const [item, setItem] = useState<Item>({});

  const [showTagInput, setShowTagInput] = useState(false);
  const [newTag, setNewTag] = useState("");

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [imagePickerType, setImagePickerType] = useState<"content">("content");

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFullscreenImage, setShowFullscreenImage] = useState(false);

  // Derived state for easier access
  const name = item.name || "";
  const content = item.content || "";
  const tags = item.tags || [];
  const contentImages = item.images || [];
  const currentLocation = item.currentLocation || "";
  const timeOfPossession = item.timeOfPossession || "";

  // Load existing data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    if (!itemId) return;

    try {
      setIsLoading(true);

      if (isEditingDraft) {
        // Load draft data
        const draftResponse = await getDraftById(itemId);
        if (draftResponse.success && draftResponse.data) {
          setItem(draftResponse.data);
        } else {
          showAlert("错误", "无法加载草稿数据", undefined, { icon: "alert-circle" });
        }
      } else {
        // Load published item data for editing
        const itemResponse = await getItemById(itemId);
        if (itemResponse.success && itemResponse.data) {
          setItem(itemResponse.data);
        } else {
          showAlert("错误", "无法加载物品数据", undefined, { icon: "alert-circle" });
        }
      }
    } catch (error) {
      console.error("Error loading data:", error);
      showAlert("错误", "加载数据时出现错误", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!item.name?.trim()) {
      newErrors.name = "请输入物品名称";
    }

    if (!item.content?.trim()) {
      newErrors.content = "请添加物品故事";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // TODO cy 这里不是每次退出都要保存，还是不能图省事，需要验证是否有更改才行
  const handleBack = () => {
    if (
      item.name ||
      item.content ||
      (item.tags || []).length > 0 ||
      (item.images || []).length > 0 ||
      item.currentLocation ||
      item.timeOfPossession
    ) {
      showAlert(
        "保存草稿",
        "您有未保存的内容，是否保存为草稿？",
        [
          { text: "不保存", style: "destructive", onPress: () => router.back() },
          { text: "保存草稿", onPress: () => handleSaveDraft() },
          { text: "取消", style: "cancel" },
        ],
        { icon: "document-text" }
      );
    } else {
      router.back();
    }
  };

  const handleSaveDraft = async () => {
    try {
      setIsLoading(true);

      const response = await saveDraft({
        ...item,
        // 为草稿手动生成一个本地 id
        id: item.id || generateId(),
        createdAt: item.createdAt || new Date().getTime(),
        updatedAt: new Date().getTime(),
      });
      if (response.success) {
        showAlert(
          "草稿已保存",
          "您的物品草稿已成功保存！",
          [{ text: "确定", onPress: () => router.back() }],
          { icon: "checkmark-circle" }
        );
      } else {
        showAlert("保存失败", response.error || "保存草稿时出现错误，请稍后重试", undefined, {
          icon: "alert-circle",
        });
      }
    } catch (error) {
      console.error("Error saving draft:", error);
      showAlert("保存失败", "保存草稿时出现错误，请稍后重试", undefined, {
        icon: "alert-circle",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      showAlert("请完善必填信息", "请检查名称和内容是否已填写", undefined, {
        icon: "information-circle",
      });
      return;
    }

    setIsLoading(true);

    const newItem: Item = {
      ...item,
      // TODO cy 这里是临时创建的时间，后面应为后端生成
      createdAt: item.createdAt || new Date().getTime(),
      updatedAt: new Date().getTime(),
      // TODO cy 这里是临时创建的 id，后面应为后端生成
      id: item.id || generateId(),
    };

    try {
      const response = await saveItem(newItem);

      if (response.success) {
        // Delete draft if we were editing one
        if (isEditingDraft && itemId) {
          await deleteDraft(itemId);
        }

        showAlert(
          "发布成功",
          "您的物品已成功发布！",
          [{ text: "确定", onPress: () => router.back() }],
          { icon: "checkmark-circle" }
        );
      } else {
        showAlert("发布失败", response.error || "发布时出现错误，请稍后重试", undefined, {
          icon: "alert-circle",
        });
      }
    } catch (error) {
      console.error("Error saving item:", error);
      showAlert("发布失败", "发布时出现错误，请稍后重试", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleContentUpload = () => {
    setImagePickerType("content");
    setShowImagePicker(true);
  };

  const handleAddContentImage = (imageUrl: string) => {
    setItem(prev => ({
      ...prev,
      images: [...(prev.images || []), imageUrl],
    }));
    setShowImagePicker(false);
  };

  const handleAddMultipleContentImages = (imageUrls: string[]) => {
    const currentImages = item.images || [];
    const remainingSlots = MAX_IMAGE_COUNT - currentImages.length;
    const imagesToAdd = imageUrls.slice(0, remainingSlots);

    if (imagesToAdd.length < imageUrls.length) {
      showAlert(
        "提示",
        `最多只能添加${MAX_IMAGE_COUNT}张图片，已为您选择前${imagesToAdd.length}张`,
        undefined,
        { icon: "information-circle" }
      );
    }

    setItem(prev => ({
      ...prev,
      images: [...(prev.images || []), ...imagesToAdd],
    }));
    setShowImagePicker(false);
  };

  const handleTakePhoto = async () => {
    try {
      const imageUrl = await takePhoto({
        allowsEditing: true,
        aspect: [1, 1],
        quality: 0.8,
      });

      if (!imageUrl) {
        // User cancelled or error occurred - close modal without showing error
        setShowImagePicker(false);
        return;
      }

      handleAddContentImage(imageUrl);
    } catch (error) {
      // Handle any unexpected errors
      console.error("Error in handleTakePhoto:", error);
      setShowImagePicker(false);
      showAlert("提示", "图片选取失败，请重试!", undefined, { icon: "image" });
    }
  };

  const handlePickFromGallery = async () => {
    try {
      setShowImagePicker(false);

      // Use multiple selection for content images
      const imageUrls = await pickMultipleFromGallery({
        quality: 0.8,
        maxCount: MAX_IMAGE_COUNT,
      });

      if (!imageUrls || imageUrls.length === 0) {
        // User cancelled or no images selected
        return;
      }

      handleAddMultipleContentImages(imageUrls);
    } catch (error) {
      console.error("Error in handlePickFromGallery:", error);
      showAlert("提示", "图片选取失败，请重试!", undefined, { icon: "image" });
    }
  };

  // Removed handleImagePress as it's now handled inline

  const handleImagePress = (index: number) => {
    setCurrentImageIndex(index);
    setShowFullscreenImage(true);
  };

  const handleRemoveImage = (index: number) => {
    setItem(prev => ({
      ...prev,
      images: (prev.images || []).filter((_, i) => i !== index),
    }));
  };

  const handleAddTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setItem(prev => ({
        ...prev,
        tags: [...(prev.tags || []), newTag.trim()],
      }));
      setNewTag("");
      setShowTagInput(false);
    }
  };

  const handleRemoveTag = (index: number) => {
    setItem(prev => ({
      ...prev,
      tags: (prev.tags || []).filter((_, i) => i !== index),
    }));
  };

  return (
    <>
      <Container
        headerProps={{
          title: itemId ? "编辑物品" : "创建物品",
          leftAction: { icon: "arrow-back", onPress: handleBack },
          rightAction: {
            text: isLoading ? "保存中..." : "发布",
            onPress: handleSave,
            disabled: isLoading,
          },
        }}
        scrollViewRef={undefined}
      >
        <View style={styles.container}>
          {/* Item Name */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>
              物品名称 <Text style={[styles.required, { color: colors.error }]}>*</Text>
            </Text>
            <TextInput
              style={[
                styles.titleInput,
                {
                  color: colors.text,
                  borderColor: errors.name ? colors.error : colors.border,
                  borderWidth: errors.name ? 2 : 1,
                },
              ]}
              value={name}
              onChangeText={text => {
                setItem(prev => ({ ...prev, name: text }));
                if (errors.name) {
                  setErrors(prev => ({ ...prev, name: undefined }));
                }
              }}
              placeholder="请输入物品名称"
              placeholderTextColor={colors.textMuted}
              maxLength={50}
            />
            {errors.name && (
              <Text style={[styles.errorText, { color: colors.error }]}>{errors.name}</Text>
            )}
          </View>

          {/* Current Location */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>当前位置</Text>
            <TextInput
              style={[
                styles.titleInput,
                {
                  color: colors.text,
                  borderColor: errors.currentLocation ? colors.error : colors.border,
                  borderWidth: errors.currentLocation ? 2 : 1,
                },
              ]}
              value={currentLocation}
              onChangeText={text => {
                setItem(prev => ({ ...prev, currentLocation: text }));
                if (errors.currentLocation) {
                  setErrors(prev => ({ ...prev, currentLocation: undefined }));
                }
              }}
              placeholder="请输入物品当前位置"
              placeholderTextColor={colors.textMuted}
              maxLength={100}
            />
            {errors.currentLocation && (
              <Text style={[styles.errorText, { color: colors.error }]}>{errors.currentLocation}</Text>
            )}
          </View>

          {/* Time of Possession */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>拥有时间</Text>
            <TextInput
              style={[
                styles.titleInput,
                {
                  color: colors.text,
                  borderColor: errors.timeOfPossession ? colors.error : colors.border,
                  borderWidth: errors.timeOfPossession ? 2 : 1,
                },
              ]}
              value={timeOfPossession}
              onChangeText={text => {
                setItem(prev => ({ ...prev, timeOfPossession: text }));
                if (errors.timeOfPossession) {
                  setErrors(prev => ({ ...prev, timeOfPossession: undefined }));
                }
              }}
              placeholder="例如：2020年至今、3年等"
              placeholderTextColor={colors.textMuted}
              maxLength={50}
            />
            {errors.timeOfPossession && (
              <Text style={[styles.errorText, { color: colors.error }]}>{errors.timeOfPossession}</Text>
            )}
          </View>

          {/* Item Content */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>
              物品故事 <Text style={[styles.required, { color: colors.error }]}>*</Text>
            </Text>
            <TextInput
              style={[
                styles.contentInput,
                {
                  color: colors.text,
                  borderColor: errors.content ? colors.error : colors.border,
                  borderWidth: errors.content ? 2 : 1,
                },
              ]}
              value={content}
              onChangeText={text => {
                setItem(prev => ({ ...prev, content: text }));
                if (errors.content) {
                  setErrors(prev => ({ ...prev, content: undefined }));
                }
              }}
              placeholder="分享这个物品背后的故事..."
              placeholderTextColor={colors.textMuted}
              multiline
              numberOfLines={6}
              textAlignVertical="top"
              maxLength={1000}
            />
            {errors.content && (
              <Text style={[styles.errorText, { color: colors.error }]}>{errors.content}</Text>
            )}
          </View>

          {/* Content Images */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionLabel, { color: colors.text }]}>故事图片</Text>
              <Pressable
                style={[styles.addButton, { backgroundColor: colors.primary }]}
                onPress={handleContentUpload}
              >
                <Ionicons name="add" size={16} color="white" />
                <Text style={[styles.addButtonText, { color: "white" }]}>添加图片</Text>
              </Pressable>
            </View>

            {contentImages.length > 0 ? (
              <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.imagesScrollView}>
                <View style={styles.imagesContainer}>
                  {contentImages.map((imageUrl, index) => (
                    <View key={index} style={styles.imageItem}>
                      <Pressable onPress={() => handleImagePress(index)}>
                        <Image source={{ uri: imageUrl }} style={styles.contentImage} />
                      </Pressable>
                      <Pressable
                        style={[styles.removeImageButton, { backgroundColor: colors.error }]}
                        onPress={() => handleRemoveImage(index)}
                      >
                        <Ionicons name="close" size={12} color="white" />
                      </Pressable>
                    </View>
                  ))}
                </View>
              </ScrollView>
            ) : (
              <View style={[styles.emptyImages, { backgroundColor: colors.backgroundTertiary }]}>
                <Ionicons name="image-outline" size={48} color={colors.textMuted} />
                <Text style={[styles.emptyImagesText, { color: colors.textMuted }]}>还没有添加图片</Text>
                <Text style={[styles.emptyImagesHint, { color: colors.textMuted }]}>
                  点击"添加图片"开始上传
                </Text>
              </View>
            )}
          </View>

          {/* Tags */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionLabel, { color: colors.text }]}>标签</Text>
              <Pressable
                style={[styles.addButton, { backgroundColor: colors.primary }]}
                onPress={() => setShowTagInput(true)}
              >
                <Ionicons name="add" size={16} color="white" />
                <Text style={[styles.addButtonText, { color: "white" }]}>添加标签</Text>
              </Pressable>
            </View>

            {showTagInput && (
              <View style={styles.tagInputContainer}>
                <TextInput
                  style={[styles.tagInput, { color: colors.text, borderColor: colors.border }]}
                  value={newTag}
                  onChangeText={setNewTag}
                  placeholder="输入标签名称"
                  placeholderTextColor={colors.textMuted}
                  maxLength={20}
                  onSubmitEditing={handleAddTag}
                />
                <Pressable
                  style={[styles.tagAddButton, { backgroundColor: colors.primary }]}
                  onPress={handleAddTag}
                >
                  <Ionicons name="checkmark" size={16} color="white" />
                </Pressable>
                <Pressable
                  style={[styles.tagCancelButton, { backgroundColor: colors.backgroundTertiary }]}
                  onPress={() => {
                    setShowTagInput(false);
                    setNewTag("");
                  }}
                >
                  <Ionicons name="close" size={16} color={colors.textMuted} />
                </Pressable>
              </View>
            )}

            {tags.length > 0 ? (
              <View style={styles.tagsContainer}>
                {tags.map((tag, index) => (
                  <View key={index} style={[styles.tag, { backgroundColor: colors.backgroundTertiary }]}>
                    <Text style={[styles.tagText, { color: colors.primary }]}>{tag}</Text>
                    <Pressable onPress={() => handleRemoveTag(index)}>
                      <Ionicons name="close" size={14} color={colors.textMuted} />
                    </Pressable>
                  </View>
                ))}
              </View>
            ) : (
              <View style={[styles.emptyTags, { backgroundColor: colors.backgroundTertiary }]}>
                <Ionicons name="pricetag-outline" size={32} color={colors.textMuted} />
                <Text style={[styles.emptyTagsText, { color: colors.textMuted }]}>还没有添加标签</Text>
              </View>
            )}
          </View>
        </View>
      </Container>

      {/* Image Picker Modal */}
      <Modal visible={showImagePicker} animationType="slide" presentationStyle="pageSheet">
        <View style={[styles.modalContainer, { backgroundColor: colors.background }]}>
          <View style={styles.modalHeader}>
            <Text style={[styles.modalTitle, { color: colors.text }]}>选择图片</Text>
            <Pressable onPress={() => setShowImagePicker(false)}>
              <Ionicons name="close" size={24} color={colors.textMuted} />
            </Pressable>
          </View>

          <Text style={[styles.multiSelectHint, { color: colors.textMuted }]}>
            最多可选择 {MAX_IMAGE_COUNT} 张
          </Text>

          <View style={styles.imagePickerOptions}>
            <Pressable
              style={[styles.imagePickerOption, { backgroundColor: colors.backgroundTertiary }]}
              onPress={handleTakePhoto}
            >
              <Ionicons name="camera" size={32} color={colors.primary} />
              <Text style={[styles.imagePickerOptionText, { color: colors.text }]}>拍照</Text>
              <Text style={[styles.imagePickerOptionSubtext, { color: colors.textMuted }]}>
                单张拍摄
              </Text>
            </Pressable>

            <Pressable
              style={[styles.imagePickerOption, { backgroundColor: colors.backgroundTertiary }]}
              onPress={handlePickFromGallery}
            >
              <Ionicons name="images" size={32} color={colors.primary} />
              <Text style={[styles.imagePickerOptionText, { color: colors.text }]}>相册</Text>
              <Text style={[styles.imagePickerOptionSubtext, { color: colors.textMuted }]}>
                多张选择
              </Text>
            </Pressable>
          </View>
        </View>
      </Modal>

      {/* Fullscreen Image Modal */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showFullscreenImage,
        images: contentImages,
        currentIndex: currentImageIndex,
        onClose: () => setShowFullscreenImage(false),
        onIndexChange: setCurrentImageIndex,
      })}
    </>
  );
}

const styles = StyleSheetCreate({
  container: {
    flex: 1,
    gap: Layout.spacing.base,
  },
  section: {
    borderRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.lg,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.base,
  },
  sectionLabel: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Layout.spacing.sm,
  },
  required: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
  },
  titleInput: {
    borderWidth: 1,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
  },
  contentInput: {
    borderWidth: 1,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.base,
    minHeight: 120,
    textAlignVertical: "top",
  },
  errorText: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
  },
  addButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    gap: Layout.spacing.xs,
  },
  addButtonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  // Images
  imagesScrollView: {
    maxHeight: 120,
  },
  imagesContainer: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
  },
  imageItem: {
    position: "relative",
  },
  contentImage: {
    width: 100,
    height: 100,
    borderRadius: Layout.borderRadius.lg,
  },
  removeImageButton: {
    position: "absolute",
    top: -6,
    right: -6,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  emptyImages: {
    padding: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.lg,
    alignItems: "center",
  },
  emptyImagesText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginTop: Layout.spacing.sm,
  },
  emptyImagesHint: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
  },
  // Tags
  tagInputContainer: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
    marginBottom: Layout.spacing.base,
  },
  tagInput: {
    flex: 1,
    borderWidth: 1,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.sm,
    fontSize: Typography.fontSize.sm,
  },
  tagAddButton: {
    width: 36,
    height: 36,
    borderRadius: Layout.borderRadius.lg,
    justifyContent: "center",
    alignItems: "center",
  },
  tagCancelButton: {
    width: 36,
    height: 36,
    borderRadius: Layout.borderRadius.lg,
    justifyContent: "center",
    alignItems: "center",
  },
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
  },
  tag: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.lg,
    gap: Layout.spacing.xs,
  },
  tagText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  emptyTags: {
    padding: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
    alignItems: "center",
  },
  emptyTagsText: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
  },
  // Modal
  modalContainer: {
    flex: 1,
    padding: Layout.spacing.lg,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.lg,
  },
  modalTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.semibold,
  },
  multiSelectHint: {
    fontSize: Typography.fontSize.sm,
    textAlign: "center",
    marginBottom: Layout.spacing.lg,
    paddingHorizontal: Layout.spacing.lg,
  },
  imagePickerOptions: {
    flexDirection: "row",
    gap: Layout.spacing.lg,
    justifyContent: "center",
  },
  imagePickerOption: {
    flex: 1,
    alignItems: "center",
    padding: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.xl,
    gap: Layout.spacing.sm,
  },
  imagePickerOptionText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
  },
  imagePickerOptionSubtext: {
    fontSize: Typography.fontSize.xs,
    textAlign: "center",
  },
});
