import { <PERSON><PERSON>, Container, ItemCard } from "@/components";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { getAllStories, StyleSheetCreate } from "@/utils";
import { showAlert } from "@/utils/alert";
import { deleteItem } from "@/utils/storage";
import { Ionicons } from "@expo/vector-icons";
import { router, useFocusEffect } from "expo-router";
import { useCallback, useRef, useState } from "react";
import { Animated, Pressable, RefreshControl, Text, View } from "react-native";

// Real item data is now loaded from local storage

export default function ObjectScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const scrollY = useRef(new Animated.Value(0)).current;

  // State for real item data
  const [stories, setStories] = useState<Item[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);

  // Edit mode state
  const [isEditMode, setIsEditMode] = useState(false);
  const [selectedStories, setSelectedStories] = useState<Set<string>>(new Set());

  // Load stories on component mount
  useFocusEffect(
    useCallback(() => {
      loadStories();
    }, [])
  );

  const loadStories = async () => {
    try {
      setIsLoading(true);
      const response = await getAllStories();
      if (response.success) {
        setStories(response.data || []);
      } else {
        console.error("Failed to load stories:", response.error);
      }
    } catch (error) {
      console.error("Error loading stories:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadStories();
    setRefreshing(false);
  };

  const handleSearch = () => {
    console.log("Search pressed");
  };

  const handleSort = () => {
    console.log("Sort pressed");
  };

  const handleItemPress = (item: Item) => {
    if (isEditMode) {
      toggleItemSelection(item.id!);
    } else {
      router.push(`/item/${item.id}`);
    }
  };

  const handleEditMode = () => {
    setIsEditMode(!isEditMode);
    setSelectedStories(new Set());
  };

  const toggleItemSelection = (itemId: string) => {
    const newSelection = new Set(selectedStories);
    if (newSelection.has(itemId)) {
      newSelection.delete(itemId);
    } else {
      newSelection.add(itemId);
    }
    setSelectedStories(newSelection);
  };

  const handleDeleteSelected = () => {
    if (selectedStories.size === 0) return;

    const itemCount = selectedStories.size;
    const message =
      itemCount === 1 ? "确定要删除这个故事吗？" : `确定要删除这 ${itemCount} 个故事吗？`;

    showAlert(
      "删除故事",
      message,
      [
        { text: "取消", style: "cancel" },
        {
          text: "删除",
          style: "destructive",
          onPress: async () => {
            try {
              const deletePromises = Array.from(selectedStories).map(id => deleteItem(id));
              await Promise.all(deletePromises);
              await loadStories();
              setSelectedStories(new Set());
              setIsEditMode(false);
            } catch (error) {
              console.error("Error deleting stories:", error);
              showAlert("错误", "删除故事时出现错误", undefined, { icon: "alert-circle" });
            }
          },
        },
      ],
      { icon: "trash" }
    );
  };

  const renderItemCard = ({ item }: { item: Item }) => {
    const isSelected = selectedStories.has(item.id!);

    return (
      <View style={styles.itemCardContainer}>
        <ItemCard item={item} onPress={handleItemPress} style={styles.itemCard} />
        {isEditMode && (
          <Pressable
            style={[
              styles.selectionButton,
              { backgroundColor: isSelected ? colors.primary : colors.backgroundTertiary },
            ]}
            onPress={() => toggleItemSelection(item.id!)}
          >
            {isSelected && <Ionicons name="checkmark" size={16} color="white" />}
          </Pressable>
        )}
      </View>
    );
  };

  const getHeaderActions = () => {
    if (isEditMode) {
      return [
        { icon: "trash" as const, onPress: handleDeleteSelected },
        { icon: "close" as const, onPress: handleEditMode },
      ];
    } else {
      return [{ icon: "create" as const, onPress: handleEditMode }];
    }
  };

  return (
    <Container
      style={{ flex: 1, backgroundColor: colors.backgroundApp }}
      headerProps={{
        title: isEditMode ? `已选择 ${selectedStories.size} 个故事` : "我的故事集",
        rightActions: getHeaderActions(),
        hideLeftAction: true,
      }}
    >
      {isLoading && stories.length === 0 ? (
        <View style={styles.emptyContainer}>
          <View style={[styles.emptyIconContainer, { backgroundColor: colors.backgroundTertiary }]}>
            <Ionicons name="hourglass-outline" size={64} color={colors.textMuted} />
          </View>
          <Text style={[styles.emptyTitle, { color: colors.text }]}>加载中...</Text>
          <Text style={[styles.emptySubText, { color: colors.textMuted }]}>正在获取您的故事</Text>
        </View>
      ) : stories.length === 0 ? (
        <View style={styles.emptyContainer}>
          <View style={[styles.emptyIconContainer, { backgroundColor: colors.backgroundTertiary }]}>
            <Ionicons name="library-outline" size={64} color={colors.textMuted} />
          </View>
          <Text style={[styles.emptyTitle, { color: colors.text }]}>故事集为空</Text>
          <Text style={[styles.emptySubText, { color: colors.textMuted }]}>
            您还没有创建任何故事{"\n"}
            开始记录生活中的美好瞬间吧
          </Text>
          <View style={styles.emptyActions}>
            <Button
              title="创建第一个故事"
              variant="primary"
              onPress={() => router.push("/item/create")}
              style={styles.createItemButton}
              icon={<Ionicons name="add" size={20} color="white" />}
              iconPosition="left"
            />
            {/* <Button
              title="了解更多"
              variant="ghost"
              onPress={() => {
                // TODO: Add help/tutorial
              }}
              style={styles.learnMoreButton}
            /> */}
          </View>
        </View>
      ) : (
        <Animated.FlatList
          data={stories}
          renderItem={renderItemCard}
          showsVerticalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          onScroll={Animated.event([{ nativeEvent: { contentOffset: { y: scrollY } } }], {
            useNativeDriver: false,
          })}
          scrollEventThrottle={16}
          refreshControl={
            <RefreshControl
              refreshing={refreshing}
              onRefresh={handleRefresh}
              tintColor={colors.primary}
            />
          }
        />
      )}
    </Container>
  );
}

const styles = StyleSheetCreate({
  listContainer: {
    paddingTop: Layout.spacing.base,
    paddingHorizontal: Layout.spacing.base,
    paddingBottom: Layout.spacing.xl,
  },
  itemCard: {
    marginBottom: Layout.spacing.base,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.xl,
    paddingVertical: Layout.spacing["4xl"],
  },
  emptyIconContainer: {
    width: 120,
    height: 120,
    borderRadius: 60,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Layout.spacing.xl,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: "700",
    textAlign: "center",
    marginBottom: Layout.spacing.base,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: "500",
    textAlign: "center",
    marginBottom: Layout.spacing.sm,
  },
  emptySubText: {
    fontSize: 16,
    textAlign: "center",
    lineHeight: 24,
    marginBottom: Layout.spacing["2xl"],
  },
  emptyActions: {
    alignItems: "center",
    gap: Layout.spacing.base,
  },
  createItemButton: {
    paddingHorizontal: Layout.spacing.xl,
  },
  learnMoreButton: {
    paddingHorizontal: Layout.spacing.lg,
  },
  itemCardContainer: {
    position: "relative",
    marginBottom: Layout.spacing.base,
  },
  selectionButton: {
    position: "absolute",
    top: 12,
    right: 12,
    width: 28,
    height: 28,
    borderRadius: 14,
    alignItems: "center",
    justifyContent: "center",
    borderWidth: 2,
    borderColor: "rgba(255, 255, 255, 0.8)",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
});
