import { Container, PremiumCard, StatCard } from "@/components";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { Pressable, Text, TouchableOpacity, View } from "react-native";

const userProfile = {
  name: "张小雨",
  description: "收藏家 · 历史爱好者",
  avatar: "https://randomuser.me/api/portraits/women/68.jpg",
  isPremium: true,
  premiumDaysRemaining: 30,
  stats: {
    items: 42,
    stories: 18,
    contributions: 127,
  },
};

const features: Array<{
  id: string;
  title: string;
  icon:
    | "checkmark-circle"
    | "diamond-outline"
    | "settings-outline"
    | "notifications-outline"
    | "information-circle";
}> = [
  { id: "privacy", title: "隐私设置", icon: "checkmark-circle" },
  { id: "notifications", title: "通知设置", icon: "notifications-outline" },
  { id: "help", title: "帮助中心", icon: "information-circle" },
  { id: "premium", title: "会员中心", icon: "diamond-outline" },
  { id: "settings", title: "系统设置", icon: "settings-outline" },
];

export default function ProfileScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const handleEditProfile = () => {
    console.log("Edit profile pressed");
  };

  const handleFeaturePress = (featureId: string) => {
    console.log("Feature pressed:", featureId);
  };

  const handleUpgradePremium = () => {
    console.log("Upgrade premium pressed");
  };

  return (
    <Container
      showHeader={false}
      enableScroll
      style={{ flex: 1, backgroundColor: colors.backgroundApp }}
    >
      {/* User Profile Section */}
      <View style={styles.profileSection}>
        <View style={styles.profileHeader}>
          <View style={styles.avatarContainer}>
            <Image source={{ uri: userProfile.avatar }} style={styles.avatar} />
            <TouchableOpacity
              style={[styles.editButton, { backgroundColor: colors.primary }]}
              onPress={handleEditProfile}
            >
              <Ionicons name="create" size={12} color="white" />
            </TouchableOpacity>
          </View>
          <View style={styles.profileInfo}>
            <Text style={[styles.userName, { color: colors.text }]}>{userProfile.name}</Text>
            <Text style={[styles.userDescription, { color: colors.textSecondary }]}>
              {userProfile.description}
            </Text>
          </View>
          {userProfile.isPremium && (
            <View style={[styles.premiumBadge]}>
              <Ionicons name="diamond" size={12} color="#5D8BF4" />
              <Text style={styles.premiumText}>高级会员</Text>
            </View>
          )}
        </View>
      </View>

      {/* Statistics Section */}
      <View style={styles.statsSection}>
        <View style={styles.statsGrid}>
          <StatCard
            value={userProfile.stats.items}
            label="收藏物品"
            color={colors.primary}
            style={styles.statCard}
          />
          <StatCard
            value={userProfile.stats.stories}
            label="创作故事"
            color={colors.primary}
            style={styles.statCard}
          />
          <StatCard
            value={userProfile.stats.contributions}
            label="草稿"
            color={colors.primary}
            style={styles.statCard}
          />
        </View>
      </View>

      {/* Features Grid */}
      <View style={styles.featuresSection}>
        {features.map(feature => (
          <Pressable
            key={feature.id}
            style={[styles.featureItem, { backgroundColor: colors.background }]}
            onPress={() => handleFeaturePress(feature.id)}
          >
            <View style={[styles.iconContainer, { backgroundColor: colors.background }]}>
              <Ionicons name={feature.icon} size={24} color={colors.primary} />
            </View>
            <Text style={[styles.featureTitle, { color: colors.text }]}>{feature.title}</Text>
          </Pressable>
        ))}
      </View>

      {/* Premium Card */}
      <View style={styles.premiumSection}>
        {userProfile.isPremium ? (
          <PremiumCard
            title="高级会员"
            subtitle="解锁全部AI功能，无限创作故事"
            daysRemaining={userProfile.premiumDaysRemaining}
            onUpgrade={handleUpgradePremium}
          />
        ) : (
          <PremiumCard
            title="升级高级会员"
            subtitle="解锁全部AI功能，享受无限创作体验"
            onUpgrade={handleUpgradePremium}
          />
        )}
      </View>
    </Container>
  );
}

const styles = StyleSheetCreate({
  profileSection: {
    paddingHorizontal: Layout.spacing.base,
    paddingTop: Layout.spacing.xl,
    marginBottom: Layout.spacing.xl,
  },
  profileHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Layout.spacing.base,
  },
  avatarContainer: {
    position: "relative",
    marginRight: Layout.spacing.base,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    borderWidth: 2,
    borderColor: "white",
  },
  editButton: {
    position: "absolute",
    bottom: 0,
    right: 0,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  profileInfo: {
    flex: 1,
  },
  userName: {
    fontSize: 20,
    fontWeight: "bold",
    marginBottom: 4,
  },
  userDescription: {
    fontSize: 14,
  },
  premiumBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(93, 139, 244, 0.1)",
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
    borderWidth: 1,
    borderColor: "rgba(93, 139, 244, 0.2)",
  },
  premiumText: {
    color: "#5D8BF4",
    fontSize: 12,
    fontWeight: "600",
  },
  statsSection: {
    marginBottom: Layout.spacing.xl,
    paddingHorizontal: Layout.spacing.base,
  },
  statsGrid: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
  },
  statCard: {
    flex: 1,
  },
  featuresSection: {
    marginBottom: Layout.spacing.xl,
    paddingHorizontal: Layout.spacing.base,
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
  },
  featureItem: {
    borderRadius: 12,
    width: 115,
    padding: Layout.spacing.base,
    alignItems: "center",
    justifyContent: "center",
    minHeight: 80,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: 8,
  },
  featureTitle: {
    fontSize: 12,
    textAlign: "center",
    fontWeight: "500",
  },
  premiumSection: {
    paddingHorizontal: Layout.spacing.base,
    marginBottom: Layout.spacing.xl,
  },
});
