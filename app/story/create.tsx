import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, But<PERSON>, Container } from "@/components";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { showAlert } from "@/utils/alert";
import { pickFromGallery, pickMultipleFromGallery, takePhoto } from "@/utils/imagePicker";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { router, useLocalSearchParams } from "expo-router";
import React, { useEffect, useState } from "react";
import { Modal, Pressable, ScrollView, Text, TextInput, View } from "react-native";
// Storage and type imports
import { MAX_IMAGE_COUNT_WITH_NORMAL_MODE, MAX_IMAGE_COUNT_WITH_TIMELINE_MODE } from "@/constants/Story";
import { Story, TimelineItem } from "@/types";
import { deleteDraft, generateId, getDraftById, getStoryById, saveDraft, saveStory } from "@/utils/storage";

interface FormErrors {
  title?: string;
  content?: string;
  coverImage?: string;
}

export default function StoryCreateScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const params = useLocalSearchParams<{ id?: string; isDraft?: string }>();
  // Extract parameters
  const storyId = params.id;
  // Determine the mode based on parameters
  const isEditingDraft = params.isDraft === "true";
  const [editingTimelineIndex, setEditingTimelineIndex] = useState<number>(-1);

  const [story, setStory] = useState<Story>({});

  const [showTagInput, setShowTagInput] = useState(false);
  const [newTag, setNewTag] = useState("");

  // UI state
  const [isLoading, setIsLoading] = useState(false);
  const [errors, setErrors] = useState<FormErrors>({});
  const [showImagePicker, setShowImagePicker] = useState(false);
  const [imagePickerType, setImagePickerType] = useState<"cover" | "content" | "timeline">("cover");

  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showFullscreenImage, setShowFullscreenImage] = useState(false);

  // Timeline fullscreen state
  const [timelineFullscreenImages, setTimelineFullscreenImages] = useState<string[]>([]);
  const [timelineCurrentImageIndex, setTimelineCurrentImageIndex] = useState(0);
  const [showTimelineFullscreenImage, setShowTimelineFullscreenImage] = useState(false);

  // Cover image fullscreen state
  const [showCoverFullscreen, setShowCoverFullscreen] = useState(false);

  // Anthropomorphism state - separate for each mode
  const [selectedImageForAnthro, setSelectedImageForAnthro] = useState<string | null>(null);

  // Derived state for easier access
  const title = story.title || "";
  const content = story.content || "";
  const contentMode = story.mode || "normal";
  const tags = story.tags || [];
  const timelineItems = story.timelineItems || [];
  const storyCoverImage = story.coverImage;
  const contentImages = story.images || [];

  // Load existing data on component mount
  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    if (!storyId) return;

    try {
      setIsLoading(true);

      if (isEditingDraft) {
        // Load draft data
        const draftResponse = await getDraftById(storyId);
        if (draftResponse.success && draftResponse.data) {
          setStory(draftResponse.data);
        } else {
          showAlert("错误", "无法加载草稿数据", undefined, { icon: "alert-circle" });
        }
      } else {
        // Load published story data for editing
        const storyResponse = await getStoryById(storyId);
        if (storyResponse.success && storyResponse.data) {
          setStory(storyResponse.data);
        } else {
          showAlert("错误", "无法加载故事数据", undefined, { icon: "alert-circle" });
        }
      }
    } catch (error) {
      console.error("Error loading data:", error);
      showAlert("错误", "加载数据时出现错误", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  // Form validation
  const validateForm = (): boolean => {
    const newErrors: FormErrors = {};

    if (!story.title?.trim()) {
      newErrors.title = "请输入故事标题";
    }

    if (story.mode !== "timeline" && !story.content) {
      newErrors.content = "请添加故事内容";
    }

    if (story.mode === "timeline" && (story.timelineItems || []).length <= 0) {
      newErrors.content = "请添加时间线节点";
    }

    if (!story.coverImage) {
      newErrors.coverImage = "请添加封面图片";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // TODO cy 这里不是每次退出都要保存，还是不能图省事，需要验证是否有更改才行
  const handleBack = () => {
    if (story.coverImage || story.title || story.content || (story.tags || []).length > 0 || (story.timelineItems || []).length > 0) {
      showAlert(
        "保存草稿",
        "您有未保存的内容，是否保存为草稿？",
        [
          { text: "不保存", style: "destructive", onPress: () => router.back() },
          { text: "保存草稿", onPress: () => handleSaveDraft() },
          { text: "取消", style: "cancel" },
        ],
        { icon: "document-text" }
      );
    } else {
      router.back();
    }
  };

  const handleSaveDraft = async () => {
    try {
      setIsLoading(true);

      const response = await saveDraft({
        ...story,
        mode: story.mode || "normal",
        // 为草稿手动生成一个本地 id
        id: story.id || generateId(),
        createdAt: story.createdAt || new Date().getTime(),
        updatedAt: new Date().getTime(),
      });
      if (response.success) {
        showAlert("草稿已保存", "您的故事草稿已成功保存！", [{ text: "确定", onPress: () => router.back() }], { icon: "checkmark-circle" });
      } else {
        showAlert("保存失败", response.error || "保存草稿时出现错误，请稍后重试", undefined, { icon: "alert-circle" });
      }
    } catch (error) {
      console.error("Error saving draft:", error);
      showAlert("保存失败", "保存草稿时出现错误，请稍后重试", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleSave = async () => {
    if (!validateForm()) {
      showAlert("请完善必填信息", "请检查标题和内容是否已填写", undefined, { icon: "information-circle" });
      return;
    }

    setIsLoading(true);

    const newStory: Story = {
      ...story,
      mode: story.mode || "normal",
      // TODO cy 这里是临时创建的时间，后面应为后端生成
      createdAt: story.createdAt || new Date().getTime(),
      updatedAt: new Date().getTime(),
      // TODO cy 这里是临时创建的 id，后面应为后端生成
      id: story.id || generateId(),
    };

    if (newStory.mode === "timeline") {
      newStory.content = "";
      newStory.images = [];
    } else {
      newStory.timelineItems = [];
    }

    try {
      if (isEditingDraft) {
        // delete draft
        const draftResponse = await deleteDraft(newStory.id!);
        if (!draftResponse.success) {
          throw new Error(draftResponse.error || "Failed to save draft");
        }
      }

      // Then publish the draft
      const publishResponse = await saveStory(newStory);
      if (!publishResponse.success) {
        throw new Error(publishResponse.error || "Failed to publish story");
      }

      showAlert("发布成功", "您的故事已成功发布！", [{ text: "确定", onPress: () => router.back() }], { icon: "checkmark-circle" });
    } catch (error) {
      console.error("Error publishing story:", error);
      showAlert("发布失败", error instanceof Error ? error.message : "请稍后重试", undefined, { icon: "alert-circle" });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCoverUpload = () => {
    setImagePickerType("cover");
    setShowImagePicker(true);
  };

  const handleContentUpload = () => {
    setImagePickerType("content");
    setShowImagePicker(true);
  };

  const handleAddCoverImage = (imageUrl: string) => {
    setStory(prev => ({ ...prev, coverImage: imageUrl }));
    setShowImagePicker(false);
  };

  const handleRemoveCoverImage = () => {
    setStory(prev => ({ ...prev, coverImage: undefined }));
  };

  const handleAddContentImage = (imageUrl: string) => {
    setStory(prev => ({ ...prev, images: [...(prev.images || []), imageUrl] }));
    setShowImagePicker(false);
  };

  const handleAddMultipleContentImages = (imageUrls: string[]) => {
    const currentImages = story.images || [];
    const remainingSlots = MAX_IMAGE_COUNT_WITH_NORMAL_MODE - currentImages.length;
    const imagesToAdd = imageUrls.slice(0, remainingSlots);

    if (imagesToAdd.length < imageUrls.length) {
      showAlert("提示", `最多只能添加${MAX_IMAGE_COUNT_WITH_NORMAL_MODE}张图片，已为您选择前${imagesToAdd.length}张`, undefined, { icon: "information-circle" });
    }

    setStory(prev => ({ ...prev, images: [...currentImages, ...imagesToAdd] }));
    setShowImagePicker(false);
  };

  const handleRemoveContentImage = (index: number) => {
    setStory(prev => ({ ...prev, images: (prev.images || []).filter((_, i) => i !== index) }));
  };

  const handleAddTimelineImage = (imageUrl: string) => {
    console.log(editingTimelineIndex, "editingTimelineIndex");
    setStory(prev => ({
      ...prev,
      timelineItems: (prev.timelineItems || []).map((item, index) => (index === editingTimelineIndex ? { ...item, images: [...(item.images || []), imageUrl] } : item)),
    }));
    setShowImagePicker(false);
  };

  const handleAddMultipleTimelineImages = (imageUrls: string[]) => {
    console.log(editingTimelineIndex, "editingTimelineIndex");
    const currentItem = story.timelineItems?.[editingTimelineIndex];
    if (!currentItem) return;

    const currentImages = currentItem.images || [];
    const remainingSlots = MAX_IMAGE_COUNT_WITH_TIMELINE_MODE - currentImages.length;
    const imagesToAdd = imageUrls.slice(0, remainingSlots);

    if (imagesToAdd.length < imageUrls.length) {
      showAlert("提示", `时间线节点最多只能添加${MAX_IMAGE_COUNT_WITH_TIMELINE_MODE}张图片，已为您选择前${imagesToAdd.length}张`, undefined, { icon: "information-circle" });
    }

    setStory(prev => ({
      ...prev,
      timelineItems: (prev.timelineItems || []).map((item, index) =>
        index === editingTimelineIndex
          ? { ...item, images: [...currentImages, ...imagesToAdd] }
          : item
      ),
    }));
    setShowImagePicker(false);
  };

  const handleTakePhoto = async () => {
    try {
      const imageUrl = await takePhoto({
        allowsEditing: true,
        aspect: imagePickerType === "cover" ? [16, 9] : [1, 1],
        quality: 0.8,
      });

      if (!imageUrl) {
        // User cancelled or error occurred - close modal without showing error
        setShowImagePicker(false);
        return;
      }

      if (imagePickerType === "cover") {
        handleAddCoverImage(imageUrl);
      } else if (imagePickerType === "content") {
        handleAddContentImage(imageUrl);
      } else if (imagePickerType === "timeline") {
        handleAddTimelineImage(imageUrl);
      }
    } catch (error) {
      // Handle any unexpected errors
      console.error('Error in handleTakePhoto:', error);
      setShowImagePicker(false);
      showAlert("提示", "图片选取失败，请重试!", undefined, { icon: "image" });
    }
  };

  const handlePickFromGallery = async () => {
    try {
      // For cover images, use single selection
      if (imagePickerType === "cover") {
        const imageUrl = await pickFromGallery({
          allowsEditing: true,
          aspect: [16, 9],
          quality: 0.8,
        });

        if (!imageUrl) {
          // User cancelled or error occurred - close modal without showing error
          setShowImagePicker(false);
          return;
        }

        handleAddCoverImage(imageUrl);
        return;
      }

      // For content and timeline images, use multiple selection
      const imageUrls = await pickMultipleFromGallery({
        quality: 0.8,
        selectionLimit: imagePickerType === "content" ? MAX_IMAGE_COUNT_WITH_NORMAL_MODE : MAX_IMAGE_COUNT_WITH_TIMELINE_MODE,
      });

      if (imageUrls.length === 0) {
        // User cancelled or error occurred - close modal without showing error
        setShowImagePicker(false);
        return;
      }

      if (imagePickerType === "content") {
        handleAddMultipleContentImages(imageUrls);
      } else if (imagePickerType === "timeline") {
        handleAddMultipleTimelineImages(imageUrls);
      }
    } catch (error) {
      // Handle any unexpected errors
      console.error('Error in handlePickFromGallery:', error);
      setShowImagePicker(false);
      showAlert("提示", "图片选取失败，请重试!", undefined, { icon: "image" });
    }
  };

  // Removed handleImagePress as it's now handled inline

  const handleTimelineImagePress = (timelineItem: TimelineItem, imageIndex: number) => {
    setTimelineFullscreenImages(timelineItem.images || []);
    setTimelineCurrentImageIndex(imageIndex);
    setShowTimelineFullscreenImage(true);
  };

  const handleCoverImagePress = () => {
    setShowCoverFullscreen(true);
  };

  const handleAddTag = () => {
    setShowTagInput(true);
  };

  const handleSaveTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setStory(prev => ({ ...prev, tags: [...(prev.tags || []), newTag.trim()] }));
      setNewTag("");
      setShowTagInput(false);
    }
  };

  const handleRemoveTag = (tagToRemove: string) => {
    setStory(prev => ({ ...prev, tags: (prev.tags || []).filter(tag => tag !== tagToRemove) }));
  };

  const handleAddTimelineItem = () => {
    if (story.timelineItems?.length) {
      const emptyTimeline = story.timelineItems.findIndex(item => item.content.trim() === "");

      if (emptyTimeline >= 0) {
        showAlert("提示", "请先完成之前的内容填写", undefined, { icon: "information-circle" });
        return;
      }
    }

    setStory(prev => ({
      ...prev,
      timelineItems: [
        ...(prev.timelineItems || []),
        {
          title: "",
          content: "",
          images: [],
        },
      ],
    }));
    setEditingTimelineIndex(story.timelineItems?.length || 0);
  };

  const handleEditTimelineItem = (updatedItem: TimelineItem, index: number) => {
    setStory(prev => ({
      ...prev,
      timelineItems: (prev.timelineItems || []).map((item, idx) => (idx === index ? updatedItem : item)),
    }));
  };

  const handleRemoveTimelineItem = (index: number) => {
    setStory(prev => ({
      ...prev,
      timelineItems: (prev.timelineItems || []).filter((_, idx) => idx !== index),
    }));
    setEditingTimelineIndex(-1);
  };

  const handleAddImageWithTimeline = () => {
    setImagePickerType("timeline");
    setShowImagePicker(true);
  };

  const handleConfirmEditTimelineItem = () => {
    const newItem = story.timelineItems?.slice(-1)![0];
    if (!newItem?.content) {
      showAlert("提示", "请填写内容", undefined, { icon: "information-circle" });
      return;
    }

    setEditingTimelineIndex(-1);
  };

  const handleAIEnhance = () => {
    if (!content.trim()) {
      showAlert("提示", "请先输入一些内容，AI将帮您润色", undefined, { icon: "bulb" });
      return;
    }
    console.log("AI enhance content");
  };

  const handleGenerate3D = () => {
    if (!storyCoverImage && contentImages.length === 0) {
      showAlert("提示", "请先上传图片，AI将为您生成3D模型", undefined, { icon: "cube" });
      return;
    }
    console.log("Generate 3D model");
  };

  const handleAISuggestion = (action: string) => {
    console.log("AI suggestion action:", action);
  };

  // Anthropomorphism handlers
  const handleAnthropomorphismImageSelect = (imageUrl: string) => {
    setSelectedImageForAnthro(imageUrl);
  };

  return (
    <>
      <Container
        style={{ flex: 1, backgroundColor: colors.backgroundApp }}
        headerProps={{
          title: storyId ? "编辑故事" : "创建新故事",
          leftAction: { icon: "arrow-back", onPress: handleBack },
          rightActions: [{ icon: isLoading ? "hourglass" : "checkmark", onPress: isLoading ? () => {} : handleSave }],
        }}
        enableScroll
      >
        <View style={[styles.content, { backgroundColor: colors.backgroundApp }]}>
          {/* Cover Images */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <View style={styles.sectionHeader}>
              <Text style={[styles.sectionLabel, { color: colors.text }]}>
                故事封面 <Text style={[styles.required, { color: colors.error }]}>*</Text>
              </Text>
              {!errors.coverImage && <Text style={[styles.errorText, { color: colors.error }]}>{errors.coverImage}</Text>}
            </View>

            {storyCoverImage ? (
              <View style={styles.heroImageContainer}>
                <Pressable onPress={handleCoverImagePress}>
                  <Image source={{ uri: storyCoverImage }} style={styles.heroImage} contentFit="cover" />
                </Pressable>
                <View style={styles.heroImageOverlay}>
                  <Pressable style={[styles.changeImageButton, { backgroundColor: "rgba(0, 0, 0, 0.6)" }]} onPress={handleCoverUpload}>
                    <Ionicons name="camera" size={20} color="white" />
                    <Text style={styles.changeImageText}>更换封面</Text>
                  </Pressable>
                  <Pressable style={[styles.removeImageButton, { backgroundColor: colors.error }]} onPress={() => handleRemoveCoverImage()}>
                    <Ionicons name="close" size={16} color="white" />
                  </Pressable>
                </View>
              </View>
            ) : (
              <Pressable
                style={[
                  styles.coverUploadLarge,
                  {
                    borderColor: errors.coverImage ? colors.error : "#e5e7eb",
                  },
                ]}
                onPress={handleCoverUpload}
              >
                <View style={styles.uploadIconContainer}>
                  <Ionicons name="camera" size={48} color={colors.primary} />
                </View>
                <Text style={[styles.coverUploadTitle, { color: colors.text }]}>添加故事封面</Text>
                <Text style={[styles.coverUploadSubtitle, { color: colors.textMuted }]}>选择一张最能代表这个故事的图片</Text>
                <Text style={[styles.coverUploadHint, { color: colors.textMuted }]}>建议尺寸 16:9，支持 JPG、PNG 格式</Text>
              </Pressable>
            )}
          </View>

          {/* Story Title */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>
              故事标题 <Text style={[styles.required, { color: colors.error }]}>*</Text>
            </Text>
            <TextInput
              style={[
                styles.titleInput,
                {
                  color: colors.text,
                  borderColor: errors.title ? colors.error : colors.border,
                  borderWidth: errors.title ? 2 : 1,
                },
              ]}
              value={title}
              onChangeText={text => {
                setStory(prev => ({ ...prev, title: text }));
                if (errors.title) {
                  setErrors(prev => ({ ...prev, title: undefined }));
                }
              }}
              placeholder="请输入故事标题"
              placeholderTextColor={colors.textMuted}
              maxLength={50}
            />
            {errors.title && <Text style={[styles.errorText, { color: colors.error }]}>{errors.title}</Text>}
            <Text style={[styles.characterCount, { color: colors.textMuted }]}>{title.length}/50</Text>
          </View>

          {/* Content Mode Selector */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>内容模式</Text>
            <View style={[styles.modeSelector, { backgroundColor: colors.backgroundTertiary }]}>
              <Pressable style={[styles.modeOption, contentMode === "normal" && { backgroundColor: colors.primary }]} onPress={() => setStory(prev => ({ ...prev, mode: "normal" }))}>
                <Ionicons name="document-text" size={20} color={contentMode === "normal" ? "white" : colors.textMuted} />
                <Text style={[styles.modeOptionText, { color: contentMode === "normal" ? "white" : colors.textMuted }]}>普通模式</Text>
              </Pressable>

              <Pressable style={[styles.modeOption, contentMode === "timeline" && { backgroundColor: colors.primary }]} onPress={() => setStory(prev => ({ ...prev, mode: "timeline" }))}>
                <Ionicons name="time" size={20} color={contentMode === "timeline" ? "white" : colors.textMuted} />
                <Text style={[styles.modeOptionText, { color: contentMode === "timeline" ? "white" : colors.textMuted }]}>时间线模式</Text>
              </Pressable>
            </View>
          </View>

          {/* Story Content */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            {contentMode === "normal" ? (
              <>
                <Text style={[styles.sectionLabel, { color: colors.text }]}>
                  物品故事 <Text style={[styles.required, { color: colors.error }]}>*</Text>
                </Text>
                <TextInput
                  style={[
                    styles.contentInput,
                    {
                      color: colors.text,
                      borderColor: errors.content ? colors.error : colors.border,
                      borderWidth: errors.content ? 2 : 1,
                    },
                  ]}
                  value={content}
                  onChangeText={text => {
                    setStory(prev => ({ ...prev, content: text }));
                    if (errors.content) {
                      setErrors(prev => ({ ...prev, content: undefined }));
                    }
                  }}
                  placeholder="讲述这个物品的故事...&#10;&#10;您可以描述：&#10;• 物品的来历和背景&#10;• 与物品相关的回忆&#10;• 物品的特殊意义&#10;• 想要传达的情感"
                  placeholderTextColor={colors.textMuted}
                  multiline
                  numberOfLines={8}
                  textAlignVertical="top"
                  maxLength={2000}
                />
                {errors.content && <Text style={[styles.errorText, { color: colors.error }]}>{errors.content}</Text>}
                <Text style={[styles.characterCount, { color: colors.textMuted }]}>{content.length}/2000</Text>

                {/* Content Images for Normal Mode */}
                <View style={styles.contentImagesSection}>
                  <View style={styles.sectionHeader}>
                    <Text style={[styles.sectionLabel, { color: colors.text }]}>物品图片</Text>
                    {contentImages.length < MAX_IMAGE_COUNT_WITH_NORMAL_MODE && <Button title="添加图片" variant="ghost" size="small" icon={<Ionicons name="camera" size={16} color={colors.primary} />} onPress={handleContentUpload} style={{ paddingHorizontal: 0 }} />}
                  </View>

                  {contentImages.length > 0 ? (
                    <View style={styles.contentImagesContainer}>
                      <ScrollView
                        horizontal
                        pagingEnabled
                        showsHorizontalScrollIndicator={false}
                        onMomentumScrollEnd={event => {
                          const index = Math.round(event.nativeEvent.contentOffset.x / event.nativeEvent.layoutMeasurement.width);
                          setCurrentImageIndex(index);
                        }}
                        style={styles.contentImagesScrollView}
                      >
                        {contentImages.map((imageUrl, index) => (
                          <Pressable
                            key={index}
                            style={styles.contentImageSlide}
                            onPress={() => {
                              setCurrentImageIndex(index);
                              setShowFullscreenImage(true);
                            }}
                          >
                            <Image source={{ uri: imageUrl }} style={styles.contentImageDisplay} />
                            <Pressable style={[styles.removeContentImageButton, { backgroundColor: colors.error }]} onPress={() => handleRemoveContentImage(index)}>
                              <Ionicons name="close" size={16} color="white" />
                            </Pressable>
                          </Pressable>
                        ))}
                      </ScrollView>

                      {/* Image Indicators */}
                      <View style={styles.contentImageIndicators}>
                        {contentImages.map((_, index) => (
                          <View
                            key={index}
                            style={[
                              styles.contentImageIndicator,
                              {
                                backgroundColor: index === currentImageIndex ? colors.primary : colors.border,
                                opacity: index === currentImageIndex ? 1 : 0.5,
                              },
                            ]}
                          />
                        ))}
                      </View>

                      <Text style={[styles.imageCountText, { color: colors.textMuted }]}>{contentImages.length} 张图片</Text>
                    </View>
                  ) : (
                    <View style={[styles.emptyContentImages, { backgroundColor: colors.backgroundTertiary }]}>
                      <Ionicons name="images-outline" size={48} color={colors.textMuted} />
                      <Text style={[styles.emptyContentImagesText, { color: colors.textMuted }]}>还没有添加图片</Text>
                      <Text style={[styles.emptyContentImagesHint, { color: colors.textMuted }]}>点击&ldquo;添加图片&rdquo;来丰富故事内容</Text>
                    </View>
                  )}
                </View>
              </>
            ) : (
              <>
                <View style={styles.timelineHeader}>
                  <Text style={[styles.sectionLabel, { color: colors.text }]}>
                    时间线内容 <Text style={[styles.required, { color: colors.error }]}>*</Text>
                  </Text>
                  <Button title="添加节点" variant="ghost" size="small" icon={<Ionicons name="add" size={16} color={colors.primary} />} onPress={handleAddTimelineItem} />
                </View>

                {timelineItems.length > 0 ? (
                  <View style={styles.timelineContainer}>
                    {timelineItems.map((item, index) => (
                      <View key={index} style={styles.timelineNode}>
                        <View style={styles.timelineNodeHeader}>
                          <View style={[styles.timelineNodeDot, { backgroundColor: colors.primary }]} />
                          {index < timelineItems.length - 1 && <View style={[styles.timelineNodeLine, { backgroundColor: colors.border }]} />}
                        </View>

                        <View style={styles.timelineNodeContent}>
                          <View style={[styles.timelineNodeCard, { backgroundColor: colors.background }]}>
                            {editingTimelineIndex === index ? (
                              <>
                                <TextInput style={[styles.timelineNodeTitle, { color: colors.text, borderColor: colors.border }]} value={item.title} onChangeText={text => handleEditTimelineItem({ ...item, title: text }, index)} placeholder="时间节点标题(选填)" placeholderTextColor={colors.textMuted} maxLength={50} />

                                <TextInput style={[styles.timelineNodeInput, { color: colors.text, borderColor: colors.border }]} value={item.content} onChangeText={text => handleEditTimelineItem({ ...item, content: text }, index)} placeholder="描述这个时间点发生的事情..." placeholderTextColor={colors.textMuted} multiline numberOfLines={4} textAlignVertical="top" maxLength={500} />
                              </>
                            ) : (
                              <>
                                <Text style={[styles.timelineNodeTitleView, { color: colors.text }]}>{item.title || "未命名节点"}</Text>
                                <Text style={[styles.timelineNodeContentView, { color: colors.textSecondary }]}>{item.content || "暂无内容"}</Text>
                              </>
                            )}

                            {/* Timeline Node Images */}
                            {(item.images?.length || editingTimelineIndex === index) && (
                              <View style={styles.timelineImagesContainer}>
                                <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.timelineImagesScrollView}>
                                  <View style={styles.timelineImagesRow}>
                                    {/* Existing Images */}
                                    {item.images?.map((imageUrl, imgIndex) => (
                                      <View key={imgIndex} style={styles.timelineImageItem}>
                                        <Pressable onPress={() => handleTimelineImagePress(item, imgIndex)}>
                                          <Image source={{ uri: imageUrl }} style={styles.timelineNodeImage} />
                                        </Pressable>
                                        {editingTimelineIndex === index && (
                                          <Pressable
                                            style={[styles.removeTimelineImageButton, { backgroundColor: colors.error }]}
                                            onPress={() => {
                                              const newImages = item.images?.filter((_, i) => i !== imgIndex) || [];
                                              handleEditTimelineItem({ ...item, images: newImages }, index);
                                            }}
                                          >
                                            <Ionicons name="close" size={12} color="white" />
                                          </Pressable>
                                        )}
                                      </View>
                                    ))}

                                    {/* Add Image Button */}
                                    {item.images!.length < MAX_IMAGE_COUNT_WITH_TIMELINE_MODE && (
                                      <Pressable style={[styles.addImageButton, { backgroundColor: colors.backgroundTertiary, borderColor: colors.border }]} onPress={handleAddImageWithTimeline}>
                                        <Ionicons name="add" size={24} color={colors.primary} />
                                      </Pressable>
                                    )}
                                  </View>
                                </ScrollView>

                                {item.images && item.images.length > 0 && <Text style={[styles.imageCountText, { color: colors.textMuted }]}>{item.images.length} 张图片</Text>}
                              </View>
                            )}

                            {/* Timeline Node Actions */}
                            {editingTimelineIndex === index ? (
                              <View style={styles.timelineNodeActions}>
                                <Pressable style={[styles.timelineNodeAction, { backgroundColor: colors.success }]} onPress={handleConfirmEditTimelineItem}>
                                  <Ionicons name="checkmark" size={16} color="white" />
                                  <Text style={[styles.timelineNodeActionText, { color: "white" }]}>确认</Text>
                                </Pressable>
                                <Pressable style={[styles.timelineNodeAction, { backgroundColor: colors.error }]} onPress={() => handleRemoveTimelineItem(index)}>
                                  <Ionicons name="trash" size={16} color="white" />
                                  <Text style={[styles.timelineNodeActionText, { color: "white" }]}>删除</Text>
                                </Pressable>
                              </View>
                            ) : (
                              <View style={styles.timelineNodeViewActions}>
                                <Pressable style={[styles.timelineNodeEditButton, { backgroundColor: colors.backgroundTertiary }]} onPress={() => setEditingTimelineIndex(index)}>
                                  <Ionicons name="create" size={16} color={colors.primary} />
                                  <Text style={[styles.timelineNodeActionText, { color: colors.primary }]}>编辑</Text>
                                </Pressable>
                              </View>
                            )}
                          </View>
                        </View>
                      </View>
                    ))}
                  </View>
                ) : (
                  <View style={[styles.emptyTimeline, { backgroundColor: colors.backgroundTertiary }]}>
                    <Ionicons name="time-outline" size={48} color={colors.textMuted} />
                    <Text style={[styles.emptyTimelineText, { color: colors.textMuted }]}>还没有时间线内容</Text>
                    <Text style={[styles.emptyTimelineHint, { color: colors.textMuted }]}>点击&ldquo;添加节点&rdquo;开始创建时间线</Text>
                  </View>
                )}
              </>
            )}
          </View>

          {/* Anthropomorphism Section */}
          {(contentImages.length > 0 || (contentMode === "timeline" && timelineItems.some(item => item.images && item.images.length > 0))) && (
            <View style={[styles.section, { backgroundColor: colors.background }]}>
              <View style={styles.sectionHeader}>
                <Text style={[styles.sectionLabel, { color: colors.text }]}>AI拟人化</Text>
              </View>

              {/* Function Introduction */}
              <View style={[styles.anthropomorphismIntroduction, { backgroundColor: "#f0f9ff" }]}>
                <View style={styles.anthropomorphismIntroHeader}>
                  <View style={[styles.anthropomorphismIntroIcon, { backgroundColor: colors.primary + "20" }]}>
                    <Ionicons name="information-circle" size={16} color={colors.primary} />
                  </View>
                  <Text style={[styles.anthropomorphismIntroTitle, { color: colors.text }]}>功能说明</Text>
                </View>

                <Text style={[styles.anthropomorphismIntroDescription, { color: colors.textMuted }]}>选择一张物品图片，AI将为您创造出独特的拟人化角色。支持多种艺术风格，让您的物品变成生动有趣的角色形象。</Text>

                <View style={styles.anthropomorphismIntroFeatures}>
                  <View style={styles.anthropomorphismIntroFeature}>
                    <Ionicons name="sunny" size={14} color={colors.primary} />
                    <Text style={[styles.anthropomorphismIntroFeatureText, { color: colors.textMuted }]}>温暖陪伴</Text>
                  </View>
                  <View style={styles.anthropomorphismIntroFeature}>
                    <Ionicons name="color-palette" size={14} color={colors.primary} />
                    <Text style={[styles.anthropomorphismIntroFeatureText, { color: colors.textMuted }]}>多种风格</Text>
                  </View>
                  <View style={styles.anthropomorphismIntroFeature}>
                    <Ionicons name="brush" size={14} color={colors.primary} />
                    <Text style={[styles.anthropomorphismIntroFeatureText, { color: colors.textMuted }]}>自由调节</Text>
                  </View>
                </View>
              </View>

              {/* Clean Image Grid */}
              <View style={styles.anthropomorphismCleanGrid}>
                {/* Content Images */}
                {contentMode === "normal"
                  ? contentImages.map((imageUrl, index) => (
                      <Pressable
                        key={index}
                        style={[
                          styles.anthropomorphismCleanImageCard,
                          { backgroundColor: colors.backgroundTertiary },
                          selectedImageForAnthro === imageUrl && {
                            borderColor: colors.primary,
                          },
                        ]}
                        onPress={() => handleAnthropomorphismImageSelect(imageUrl)}
                      >
                        <Image source={{ uri: imageUrl }} style={styles.anthropomorphismCleanImage} />
                        {selectedImageForAnthro === imageUrl && (
                          <View style={[styles.anthropomorphismCleanSelected, { backgroundColor: colors.primary }]}>
                            <Ionicons name="checkmark" size={16} color="white" />
                          </View>
                        )}
                      </Pressable>
                    ))
                  : timelineItems.flatMap((item, itemIndex) =>
                      (item.images || []).map((imageUrl, imgIndex) => (
                        <Pressable
                          key={`${itemIndex}-${imgIndex}`}
                          style={[
                            styles.anthropomorphismCleanImageCard,
                            { backgroundColor: colors.backgroundTertiary },
                            selectedImageForAnthro === imageUrl && {
                              borderColor: colors.primary,
                            },
                          ]}
                          onPress={() => handleAnthropomorphismImageSelect(imageUrl)}
                        >
                          <Image source={{ uri: imageUrl }} style={styles.anthropomorphismCleanImage} />
                          {selectedImageForAnthro === imageUrl && (
                            <View style={[styles.anthropomorphismCleanSelected, { backgroundColor: colors.primary }]}>
                              <Ionicons name="checkmark" size={16} color="white" />
                            </View>
                          )}
                        </Pressable>
                      ))
                    )}
              </View>

              {/* Simple Continue Button */}
              {selectedImageForAnthro && (
                <View style={styles.anthropomorphismCleanContinue}>
                  <Button title="开始创建" variant="primary" onPress={() => router.push(`/story/anthropomorphism?storyId=${story.id || "new"}&selectedImage=${encodeURIComponent(selectedImageForAnthro)}`)} icon={<Ionicons name="arrow-forward" size={16} color="white" />} style={styles.anthropomorphismCleanButton} />
                </View>
              )}
            </View>
          )}

          {/* Tags */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>标签</Text>

            <View style={styles.tagsContainer}>
              {tags.map((tag, index) => (
                <View key={index} style={styles.customTagChip}>
                  <Text style={[styles.customTagText, { color: colors.primary }]}>{tag}</Text>
                  <Pressable onPress={() => handleRemoveTag(tag)} style={[styles.customTagRemove, { backgroundColor: colors.primary }]}>
                    <Ionicons name="close" size={12} color="white" />
                  </Pressable>
                </View>
              ))}

              {showTagInput ? (
                <View style={styles.tagInputContainer}>
                  <TextInput style={[styles.tagInput, { color: colors.text, borderColor: colors.primary }]} value={newTag} onChangeText={setNewTag} placeholder="输入标签" placeholderTextColor={colors.textMuted} autoFocus onSubmitEditing={handleSaveTag} maxLength={10} />
                  <Pressable onPress={handleSaveTag} style={[styles.tagSaveButton, { backgroundColor: colors.primary }]}>
                    <Ionicons name="checkmark" size={16} color="white" />
                  </Pressable>
                  <Pressable onPress={() => setShowTagInput(false)} style={[styles.tagCancelButton, { backgroundColor: colors.textMuted }]}>
                    <Ionicons name="close" size={16} color="white" />
                  </Pressable>
                </View>
              ) : (
                <Pressable onPress={handleAddTag} style={[styles.addTagButton, { backgroundColor: colors.backgroundTertiary, borderColor: colors.border }]}>
                  <Ionicons name="add" size={16} color={colors.primary} />
                  <Text style={[styles.addTagText, { color: colors.primary }]}>添加标签</Text>
                </Pressable>
              )}
            </View>

            {/* Suggested Tags */}
            {/* // TODO cy 推荐标签 */}
            <View style={styles.suggestedTags}>
              <Text style={[styles.suggestedTagsLabel, { color: colors.textMuted }]}>推荐标签：</Text>
              <View style={styles.suggestedTagsContainer}>
                {["家族记忆", "老物件", "收藏品", "纪念品", "传承", "回忆"].map(suggestedTag => (
                  <Pressable key={suggestedTag} style={[styles.suggestedTag, { backgroundColor: colors.backgroundTertiary, borderColor: colors.border }]} onPress={() => setStory(prev => ({ ...prev, tags: [...(prev.tags || []), suggestedTag] }))}>
                    <Text style={[styles.suggestedTagText, { color: colors.textSecondary }]}>{suggestedTag}</Text>
                  </Pressable>
                ))}
              </View>
            </View>
          </View>

          {/* AI Assistant */}
          <View style={[styles.section, { backgroundColor: colors.background }]}>
            <Text style={[styles.sectionLabel, { color: colors.text }]}>AI助手</Text>

            {(storyCoverImage || contentImages.length > 0) && (
              <AIAssistantCard
                type="suggestion"
                message="检测到您添加了图片，AI可以帮您："
                actions={[
                  { title: "生成3D模型", icon: "cube", onPress: () => handleAISuggestion("3d") },
                  { title: "图片增强", icon: "image", onPress: () => handleAISuggestion("enhance") },
                  { title: "暂不需要", variant: "outline", onPress: () => handleAISuggestion("skip") },
                ]}
              />
            )}

            {content.length > 50 && (
              <AIAssistantCard
                type="suggestion"
                message="内容已有一定长度，是否需要AI润色？"
                actions={[
                  { title: "AI润色", icon: "sparkles", onPress: handleAIEnhance },
                  { title: "语法检查", icon: "checkmark-circle", onPress: () => handleAISuggestion("grammar") },
                  { title: "暂不需要", variant: "outline", onPress: () => handleAISuggestion("skip") },
                ]}
              />
            )}

            <AIAssistantCard
              type="assistant"
              title="AI创作助手"
              message="让AI帮您完善故事内容"
              actions={[
                { title: "AI润色内容", icon: "sparkles", onPress: handleAIEnhance },
                { title: "3D建模", icon: "cube", onPress: handleGenerate3D },
                { title: "生成标签", icon: "bookmark", onPress: () => handleAISuggestion("tags") },
                { title: "语音转文字", icon: "mic", onPress: () => handleAISuggestion("speech") },
              ]}
            />
          </View>

          {/* Action Buttons */}
          <View style={styles.actionButtons}>
            <Button title="保存草稿" variant="outline" onPress={handleSaveDraft} style={styles.actionButton} icon={<Ionicons name="document-text" size={16} color={colors.primary} />} disabled={isLoading} />
            <Button title={isLoading ? "发布中..." : "发布故事"} variant="primary" onPress={handleSave} style={styles.actionButton} icon={isLoading ? <Ionicons name="hourglass" size={16} color="white" /> : <Ionicons name="send" size={16} color="white" />} disabled={isLoading} />
          </View>
        </View>
      </Container>

      {/* Image Picker Modal */}
      <Modal visible={showImagePicker} transparent animationType="slide" onRequestClose={() => setShowImagePicker(false)}>
        <Pressable style={styles.modalOverlay} onPress={() => setShowImagePicker(false)}>
          <Pressable style={[styles.imagePickerModal, { backgroundColor: colors.background }]} onPress={(e) => e.stopPropagation()}>
            <View style={styles.modalHeader}>
              <Text style={[styles.modalTitle, { color: colors.text }]}>
                {imagePickerType === "cover" ? "选择封面图片" : "选择图片"}
              </Text>
              <Pressable onPress={() => setShowImagePicker(false)}>
                <Ionicons name="close" size={24} color={colors.textMuted} />
              </Pressable>
            </View>

            {imagePickerType !== "cover" && (
              <Text style={[styles.multiSelectHint, { color: colors.textMuted }]}>
                可以选择多张图片，最多{imagePickerType === "content" ? MAX_IMAGE_COUNT_WITH_NORMAL_MODE : MAX_IMAGE_COUNT_WITH_TIMELINE_MODE}张
              </Text>
            )}

            <View style={styles.imagePickerOptions}>
              <Pressable style={[styles.imagePickerOption, { backgroundColor: colors.backgroundTertiary }]} onPress={handleTakePhoto}>
                <Ionicons name="camera" size={32} color={colors.primary} />
                <Text style={[styles.imagePickerOptionText, { color: colors.text }]}>拍照</Text>
                <Text style={[styles.imagePickerOptionSubtext, { color: colors.textMuted }]}>单张拍摄</Text>
              </Pressable>

              <Pressable style={[styles.imagePickerOption, { backgroundColor: colors.backgroundTertiary }]} onPress={handlePickFromGallery}>
                <Ionicons name="images" size={32} color={colors.primary} />
                <Text style={[styles.imagePickerOptionText, { color: colors.text }]}>相册</Text>
                <Text style={[styles.imagePickerOptionSubtext, { color: colors.textMuted }]}>
                  {imagePickerType === "cover" ? "单张选择" : "多张选择"}
                </Text>
              </Pressable>
            </View>
          </Pressable>
        </Pressable>
      </Modal>

      {/* Fullscreen Image Modal for Normal Mode */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showFullscreenImage,
        images: contentImages,
        currentIndex: currentImageIndex,
        onClose: () => setShowFullscreenImage(false),
        onIndexChange: setCurrentImageIndex,
      })}

      {/* Fullscreen Image Modal for Timeline Mode */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showTimelineFullscreenImage,
        images: timelineFullscreenImages,
        currentIndex: timelineCurrentImageIndex,
        onClose: () => setShowTimelineFullscreenImage(false),
        onIndexChange: setTimelineCurrentImageIndex,
      })}

      {/* Fullscreen Image Modal for Cover Image */}
      {React.createElement(require("@/components/FullscreenImageModal").default, {
        visible: showCoverFullscreen,
        images: storyCoverImage ? [storyCoverImage] : [],
        currentIndex: 0,
        onClose: () => setShowCoverFullscreen(false),
        onIndexChange: () => {}, // No index change needed for single image
      })}
    </>
  );
}

const styles = StyleSheetCreate({
  content: {
    flex: 1,
    paddingHorizontal: Layout.spacing.base,
  },
  section: {
    marginBottom: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
  },
  sectionLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Layout.spacing.sm,
  },
  sectionHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.sm,
  },

  // Form validation
  required: {
    fontSize: Typography.fontSize.sm,
  },
  errorText: {
    fontSize: Typography.fontSize.xs,
    marginTop: Layout.spacing.xs,
  },
  characterCount: {
    fontSize: Typography.fontSize.xs,
    textAlign: "right",
    marginTop: Layout.spacing.xs,
  },

  // Hero Cover Image
  heroImageContainer: {
    height: 280,
    borderRadius: Layout.borderRadius.xl,
    overflow: "hidden",
    position: "relative",
    marginBottom: Layout.spacing.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  heroImage: {
    width: "100%",
    height: "100%",
  },
  heroImageOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "space-between",
    alignItems: "flex-end",
    padding: Layout.spacing.base,
    flexDirection: "row",
  },
  changeImageButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    gap: Layout.spacing.xs,
  },
  changeImageText: {
    color: "white",
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  removeImageButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },

  // Large Cover Upload
  coverUploadLarge: {
    height: 280,
    borderRadius: Layout.borderRadius.xl,
    justifyContent: "center",
    alignItems: "center",
    padding: Layout.spacing.xl,
    borderWidth: 2,
    borderStyle: "dashed",
  },
  uploadIconContainer: {
    width: 80,
    height: 80,
    borderRadius: 40,
    backgroundColor: "rgba(59, 130, 246, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginBottom: Layout.spacing.lg,
  },
  coverUploadTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Layout.spacing.sm,
    textAlign: "center",
  },
  coverUploadSubtitle: {
    fontSize: Typography.fontSize.base,
    marginBottom: Layout.spacing.base,
    textAlign: "center",
    lineHeight: 22,
  },
  coverUploadHint: {
    fontSize: Typography.fontSize.sm,
    textAlign: "center",
    lineHeight: 18,
  },

  // Form inputs
  titleInput: {
    borderWidth: 2,
    borderRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.lg,
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  contentInput: {
    borderWidth: 2,
    borderRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.lg,
    fontSize: Typography.fontSize.base,
    lineHeight: 24,
    minHeight: 220,
    textAlignVertical: "top",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },

  // Timeline
  emptyTimeline: {
    padding: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.lg,
    alignItems: "center",
  },
  emptyTimelineText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginTop: Layout.spacing.sm,
  },
  emptyTimelineHint: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
    textAlign: "center",
  },

  // Tags
  tagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
    marginBottom: Layout.spacing.sm,
  },
  customTagChip: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#ffffff",
    borderRadius: Layout.borderRadius.xl,
    paddingLeft: Layout.spacing.base,
    paddingRight: Layout.spacing.xs,
    paddingVertical: Layout.spacing.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
    borderWidth: 2,
    borderColor: "#e5e7eb",
  },
  customTagText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    marginRight: Layout.spacing.xs,
  },
  customTagRemove: {
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  addTagButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.xl,
    borderWidth: 2,
    borderStyle: "dashed",
    gap: Layout.spacing.xs,
  },
  addTagText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  tagInputContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: Layout.spacing.xs,
  },
  tagInput: {
    borderWidth: 2,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.sm,
    minWidth: 120,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  tagSaveButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  tagCancelButton: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  suggestedTags: {
    marginTop: Layout.spacing.sm,
  },
  suggestedTagsLabel: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Layout.spacing.sm,
  },
  suggestedTagsContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.xs,
  },
  suggestedTag: {
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    borderWidth: 1,
    backgroundColor: "#ffffff",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  suggestedTagText: {
    fontSize: Typography.fontSize.sm,
  },

  // Action buttons
  actionButtons: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
    marginTop: Layout.spacing.lg,
    marginBottom: Layout.spacing.xl,
  },
  actionButton: {
    flex: 1,
  },

  addButtonText: {
    fontSize: Typography.fontSize.sm,
  },

  // Content Mode Selector
  modeSelector: {
    flexDirection: "row",
    borderRadius: Layout.borderRadius.xl,
    padding: 4,
    backgroundColor: "#f8f9fa",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
  },
  modeOption: {
    flex: 1,
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    paddingVertical: Layout.spacing.base,
    paddingHorizontal: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    marginHorizontal: 2,
    gap: Layout.spacing.xs,
  },
  modeOptionText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
  },

  // Timeline Styles
  timelineHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.base,
  },
  timelineContainer: {
    gap: Layout.spacing.lg,
  },
  timelineNode: {
    flexDirection: "row",
    gap: Layout.spacing.base,
  },
  timelineNodeHeader: {
    alignItems: "center",
    paddingTop: Layout.spacing.sm,
  },
  timelineNodeDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 3,
    borderColor: "white",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  timelineNodeLine: {
    width: 3,
    height: 60,
    marginTop: Layout.spacing.sm,
  },
  timelineNodeContent: {
    flex: 1,
  },
  timelineNodeCard: {
    borderRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.lg,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 8,
    elevation: 3,
    gap: Layout.spacing.sm,
  },
  timelineNodeTitle: {
    borderWidth: 2,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  timelineNodeInput: {
    borderWidth: 2,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.sm,
    lineHeight: 20,
    minHeight: 100,
    textAlignVertical: "top",
  },
  timelineNodeImageContainer: {
    position: "relative",
    alignSelf: "flex-start",
  },
  timelineImagesContainer: {
    marginTop: Layout.spacing.sm,
  },
  timelineImagesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.xs,
    marginBottom: Layout.spacing.xs,
  },
  timelineImageItem: {
    position: "relative",
  },
  timelineNodeImage: {
    width: 80,
    height: 80,
    borderRadius: Layout.borderRadius.lg,
  },
  removeTimelineImageButton: {
    position: "absolute",
    top: 0,
    right: -6,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  imageCountText: {
    fontSize: Typography.fontSize.xs,
    marginTop: Layout.spacing.xs,
  },
  timelineNodeActions: {
    flexDirection: "row",
    gap: Layout.spacing.sm,
    marginTop: Layout.spacing.sm,
  },
  timelineNodeAction: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    gap: Layout.spacing.xs,
  },
  timelineNodeActionText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  timelineImagesScrollView: {
    maxHeight: 90,
  },
  timelineImagesRow: {
    flexDirection: "row",
    alignItems: "center",
    gap: Layout.spacing.md,
    paddingRight: Layout.spacing.base,
  },
  addImageButton: {
    width: 80,
    height: 80,
    borderRadius: Layout.borderRadius.lg,
    borderWidth: 2,
    borderStyle: "dashed",
    justifyContent: "center",
    alignItems: "center",
  },
  timelineNodeViewActions: {
    flexDirection: "row",
    justifyContent: "flex-end",
    marginTop: Layout.spacing.sm,
  },
  timelineNodeEditButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    gap: Layout.spacing.xs,
  },
  timelineNodeTitleView: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Layout.spacing.sm,
  },
  timelineNodeContentView: {
    fontSize: Typography.fontSize.base,
    lineHeight: 22,
    marginBottom: Layout.spacing.sm,
  },

  // Modals
  modalOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "flex-end",
  },
  imagePickerModal: {
    borderTopLeftRadius: Layout.borderRadius.xl,
    borderTopRightRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.xl,
    minHeight: 240,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: -4 },
    shadowOpacity: 0.1,
    shadowRadius: 12,
    elevation: 8,
  },
  modalHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: Layout.spacing.lg,
  },
  modalTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
  },
  imagePickerOptions: {
    flexDirection: "row",
    gap: Layout.spacing.lg,
    justifyContent: "center",
  },
  imagePickerOption: {
    padding: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.xl,
    alignItems: "center",
    minWidth: 120,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 6,
    elevation: 3,
  },
  imagePickerOptionText: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  imagePickerOptionSubtext: {
    fontSize: Typography.fontSize.xs,
    marginTop: Layout.spacing.xs,
    textAlign: "center",
  },
  multiSelectHint: {
    fontSize: Typography.fontSize.sm,
    textAlign: "center",
    marginBottom: Layout.spacing.lg,
    paddingHorizontal: Layout.spacing.lg,
  },

  // Content Images for Normal Mode
  contentImagesSection: {
    marginTop: Layout.spacing.lg,
  },
  contentImagesContainer: {
    // marginTop: Layout.spacing.sm,
  },
  contentImagesScrollView: {
    height: 200,
  },
  contentImageSlide: {
    width: 300,
    height: 200,
    marginRight: Layout.spacing.sm,
    position: "relative",
  },
  contentImageDisplay: {
    width: "100%",
    height: "100%",
    borderRadius: Layout.borderRadius.lg,
  },
  removeContentImageButton: {
    position: "absolute",
    top: 8,
    right: 8,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  contentImageIndicators: {
    flexDirection: "row",
    justifyContent: "center",
    marginTop: Layout.spacing.sm,
    gap: Layout.spacing.xs,
  },
  contentImageIndicator: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  emptyContentImages: {
    padding: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.lg,
    alignItems: "center",
    marginTop: Layout.spacing.sm,
  },
  emptyContentImagesText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    marginTop: Layout.spacing.sm,
  },
  emptyContentImagesHint: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
    textAlign: "center",
  },

  // Anthropomorphism styles
  sectionSubtitle: {
    fontSize: Typography.fontSize.sm,
    marginTop: Layout.spacing.xs,
  },
  anthropomorphismImageSelection: {
    marginBottom: Layout.spacing.lg,
  },
  anthropomorphismStepTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Layout.spacing.sm,
  },
  anthropomorphismStepSubtitle: {
    fontSize: Typography.fontSize.sm,
    marginBottom: Layout.spacing.sm,
  },
  anthropomorphismImagesScroll: {
    marginTop: Layout.spacing.sm,
  },
  anthropomorphismImageOption: {
    width: 100,
    height: 100,
    marginRight: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    borderWidth: 2,
    borderColor: "transparent",
    position: "relative",
    overflow: "hidden",
  },
  anthropomorphismImagePreview: {
    width: "100%",
    height: "100%",
  },
  anthropomorphismImageLabel: {
    position: "absolute",
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    paddingVertical: Layout.spacing.xs,
    paddingHorizontal: Layout.spacing.xs,
  },
  anthropomorphismImageLabelText: {
    fontSize: Typography.fontSize.xs,
    color: "white",
    textAlign: "center",
  },
  anthropomorphismImageSelected: {
    position: "absolute",
    top: Layout.spacing.xs,
    right: Layout.spacing.xs,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
  },
  anthropomorphismStyleSelection: {
    marginBottom: Layout.spacing.lg,
  },
  anthropomorphismStylesGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
    marginTop: Layout.spacing.sm,
  },
  anthropomorphismStyleOption: {
    width: "48%",
    padding: Layout.spacing.base,
    borderRadius: Layout.borderRadius.lg,
    borderWidth: 2,
    borderColor: "transparent",
    alignItems: "center",
    position: "relative",
  },
  anthropomorphismStylePreview: {
    fontSize: 32,
    marginBottom: Layout.spacing.xs,
  },
  anthropomorphismStyleName: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Layout.spacing.xs,
    textAlign: "center",
  },
  anthropomorphismStyleDescription: {
    fontSize: Typography.fontSize.xs,
    textAlign: "center",
  },
  anthropomorphismStyleSelected: {
    position: "absolute",
    top: Layout.spacing.xs,
    right: Layout.spacing.xs,
    width: 20,
    height: 20,
    borderRadius: 10,
    justifyContent: "center",
    alignItems: "center",
  },
  anthropomorphismItemDescription: {
    marginBottom: Layout.spacing.lg,
  },
  anthropomorphismDescriptionInput: {
    borderWidth: 1,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.base,
    fontSize: Typography.fontSize.sm,
    minHeight: 80,
    textAlignVertical: "top",
  },
  anthropomorphismGenerateSection: {
    marginBottom: Layout.spacing.lg,
  },
  anthropomorphismGenerateButton: {
    marginTop: Layout.spacing.sm,
  },
  anthropomorphismResults: {
    marginTop: Layout.spacing.lg,
  },
  anthropomorphismResultsScroll: {
    marginTop: Layout.spacing.sm,
  },
  anthropomorphismResultCard: {
    width: 150,
    marginRight: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.sm,
    position: "relative",
  },
  anthropomorphismResultImage: {
    width: "100%",
    height: 100,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.sm,
  },
  anthropomorphismResultInfo: {
    marginBottom: Layout.spacing.sm,
  },
  anthropomorphismResultStyle: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Layout.spacing.xs,
  },
  anthropomorphismResultDescription: {
    fontSize: Typography.fontSize.xs,
  },
  anthropomorphismResultAction: {
    position: "absolute",
    top: Layout.spacing.sm,
    right: Layout.spacing.sm,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
  },

  // New Anthropomorphism Preview Styles
  anthropomorphismPreview: {
    padding: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.lg,
    marginBottom: Layout.spacing.base,
  },
  anthropomorphismPreviewContent: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Layout.spacing.base,
  },
  anthropomorphismPreviewIcon: {
    width: 40,
    height: 40,
    borderRadius: 30,
    backgroundColor: "rgba(59, 130, 246, 0.1)",
    justifyContent: "center",
    alignItems: "center",
    marginRight: Layout.spacing.base,
  },
  anthropomorphismPreviewText: {
    flex: 1,
  },
  anthropomorphismPreviewTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Layout.spacing.xs,
  },
  anthropomorphismPreviewSubtitle: {
    fontSize: Typography.fontSize.sm,
    lineHeight: 20,
  },
  anthropomorphismModeNote: {
    fontSize: Typography.fontSize.xs,
    marginTop: Layout.spacing.xs,
    fontStyle: "italic",
  },

  anthropomorphismContinueSection: {
    marginTop: Layout.spacing.base,
    paddingTop: Layout.spacing.base,
    borderTopWidth: 1,
    borderTopColor: "rgba(0, 0, 0, 0.1)",
  },
  anthropomorphismContinueButton: {
    marginTop: Layout.spacing.sm,
  },

  // Enhanced Anthropomorphism Card Styles
  anthropomorphismCard: {
    padding: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.xl,
    marginBottom: Layout.spacing.base,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  anthropomorphismCardHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Layout.spacing.lg,
  },
  anthropomorphismIconContainer: {
    width: 60,
    height: 60,
    borderRadius: 30,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Layout.spacing.base,
  },
  anthropomorphismHeaderText: {
    flex: 1,
  },
  anthropomorphismCardTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Layout.spacing.xs,
  },
  anthropomorphismCardSubtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: 22,
  },
  anthropomorphismModeIndicator: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    marginBottom: Layout.spacing.lg,
  },
  anthropomorphismModeText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    marginLeft: Layout.spacing.sm,
  },
  anthropomorphismImageGrid: {
    marginBottom: Layout.spacing.base,
  },
  anthropomorphismImagesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
    marginTop: Layout.spacing.base,
  },
  anthropomorphismImageCard: {
    width: "48%",
    borderRadius: Layout.borderRadius.lg,
    padding: Layout.spacing.sm,
    borderWidth: 1,
    borderColor: "transparent",
    position: "relative",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  anthropomorphismImageCardPreview: {
    width: "100%",
    height: 100,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.sm,
  },
  anthropomorphismImageCardInfo: {
    alignItems: "center",
  },
  anthropomorphismImageCardLabel: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Layout.spacing.xs,
    textAlign: "center",
  },
  anthropomorphismImageCardType: {
    fontSize: Typography.fontSize.xs,
    textAlign: "center",
  },
  anthropomorphismImageCardSelected: {
    position: "absolute",
    top: Layout.spacing.sm,
    right: Layout.spacing.sm,
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
  },
  anthropomorphismMoreIndicator: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    padding: Layout.spacing.base,
    borderRadius: Layout.borderRadius.lg,
    marginTop: Layout.spacing.sm,
  },
  anthropomorphismMoreText: {
    fontSize: Typography.fontSize.sm,
    marginLeft: Layout.spacing.sm,
  },
  anthropomorphismSelectedPreview: {
    flexDirection: "row",
    alignItems: "center",
    padding: Layout.spacing.base,
    borderRadius: Layout.borderRadius.lg,
    marginBottom: Layout.spacing.base,
  },
  anthropomorphismSelectedImage: {
    width: 60,
    height: 60,
    borderRadius: Layout.borderRadius.md,
    marginRight: Layout.spacing.base,
  },
  anthropomorphismSelectedInfo: {
    flex: 1,
  },
  anthropomorphismSelectedTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    marginBottom: Layout.spacing.xs,
  },
  anthropomorphismSelectedSubtitle: {
    fontSize: Typography.fontSize.sm,
  },

  // Premium Anthropomorphism Card Styles
  anthropomorphismPremiumCard: {
    marginBottom: Layout.spacing.base,
    borderRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.xl,
    position: "relative",
    overflow: "hidden",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.15,
    shadowRadius: 12,
    elevation: 8,
    borderWidth: 1,
    borderColor: "rgba(255, 255, 255, 0.1)",
  },
  anthropomorphismGradientOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    opacity: 0.6,
  },
  anthropomorphismPremiumHeader: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: Layout.spacing.lg,
    zIndex: 1,
  },
  anthropomorphismHeaderLeft: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  anthropomorphismPremiumIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Layout.spacing.base,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 4,
  },
  anthropomorphismHeaderContent: {
    flex: 1,
  },
  anthropomorphismPremiumTitle: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Layout.spacing.xs,
  },
  anthropomorphismPremiumSubtitle: {
    fontSize: Typography.fontSize.base,
    lineHeight: 20,
  },
  anthropomorphismModeBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.lg,
    borderWidth: 1,
  },
  anthropomorphismModeLabel: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.semibold,
    marginLeft: Layout.spacing.xs,
  },
  anthropomorphismFeatures: {
    flexDirection: "row",
    justifyContent: "space-around",
    marginBottom: Layout.spacing.lg,
    paddingHorizontal: Layout.spacing.sm,
  },
  anthropomorphismFeatureItem: {
    alignItems: "center",
    flex: 1,
  },
  anthropomorphismFeatureIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginBottom: Layout.spacing.xs,
  },
  anthropomorphismFeatureText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
    textAlign: "center",
  },

  // Premium Image Selection Styles
  anthropomorphismSelectionHeader: {
    marginBottom: Layout.spacing.lg,
    alignItems: "center",
  },
  anthropomorphismSelectionTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Layout.spacing.xs,
    textAlign: "center",
  },
  anthropomorphismSelectionSubtitle: {
    fontSize: Typography.fontSize.sm,
    textAlign: "center",
    lineHeight: 20,
  },
  anthropomorphismImageGallery: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.base,
    justifyContent: "space-between",
  },
  anthropomorphismPremiumImageCard: {
    width: "48%",
    borderRadius: Layout.borderRadius.xl,
    padding: Layout.spacing.sm,
    position: "relative",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  anthropomorphismPremiumImageSelected: {
    transform: [{ scale: 0.98 }],
    shadowColor: "#3B82F6",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    elevation: 8,
  },
  anthropomorphismImageWrapper: {
    position: "relative",
    marginBottom: Layout.spacing.sm,
  },
  anthropomorphismPremiumImagePreview: {
    width: "100%",
    height: 120,
    borderRadius: Layout.borderRadius.lg,
  },
  anthropomorphismSelectionOverlay: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    borderRadius: Layout.borderRadius.lg,
    justifyContent: "center",
    alignItems: "center",
  },
  anthropomorphismCheckIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  anthropomorphismImageMeta: {
    alignItems: "center",
  },
  anthropomorphismImageBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.md,
    marginBottom: Layout.spacing.xs,
  },
  anthropomorphismBadgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.semibold,
    marginLeft: Layout.spacing.xs,
  },
  anthropomorphismImageTitle: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    textAlign: "center",
  },

  // Premium Continue Section Styles
  anthropomorphismPremiumContinue: {
    marginTop: Layout.spacing.lg,
    padding: Layout.spacing.lg,
    borderRadius: Layout.borderRadius.xl,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "rgba(59, 130, 246, 0.2)",
  },
  anthropomorphismContinueHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Layout.spacing.base,
  },
  anthropomorphismContinueIcon: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Layout.spacing.sm,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  anthropomorphismContinueText: {
    alignItems: "center",
  },
  anthropomorphismContinueTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    marginBottom: Layout.spacing.xs,
  },
  anthropomorphismContinueSubtitle: {
    fontSize: Typography.fontSize.sm,
    textAlign: "center",
  },
  anthropomorphismSelectedImagePreview: {
    width: 80,
    height: 80,
    borderRadius: Layout.borderRadius.lg,
    marginBottom: Layout.spacing.base,
    position: "relative",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    elevation: 4,
  },
  anthropomorphismSelectedImageThumb: {
    width: "100%",
    height: "100%",
    borderRadius: Layout.borderRadius.lg,
  },
  anthropomorphismPreviewOverlay: {
    position: "absolute",
    top: -8,
    right: -8,
    width: 32,
    height: 32,
    borderRadius: 16,
    backgroundColor: "white",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 4,
  },
  anthropomorphismPremiumButton: {
    paddingVertical: Layout.spacing.base,
    paddingHorizontal: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.xl,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
    elevation: 6,
  },
  anthropomorphismCleanHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Layout.spacing.lg,
  },
  anthropomorphismCleanIcon: {
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Layout.spacing.sm,
  },
  anthropomorphismCleanTitle: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
  },
  anthropomorphismCleanGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
  },
  anthropomorphismCleanImageCard: {
    width: "30%",
    aspectRatio: 1,
    borderRadius: Layout.borderRadius.lg,
    overflow: "hidden",
    position: "relative",
    borderWidth: 3,
    borderColor: "transparent",
  },
  anthropomorphismCleanImage: {
    width: "100%",
    height: "100%",
  },
  anthropomorphismCleanSelected: {
    position: "absolute",
    top: 8,
    right: 8,
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  anthropomorphismCleanContinue: {
    marginTop: Layout.spacing.lg,
    alignItems: "center",
  },
  anthropomorphismCleanButton: {
    paddingHorizontal: Layout.spacing.xl,
    borderRadius: Layout.borderRadius.xl,
  },

  // Introduction Section Styles
  anthropomorphismIntroduction: {
    padding: Layout.spacing.base,
    borderRadius: Layout.borderRadius.lg,
    marginBottom: Layout.spacing.lg,
    borderWidth: 1,
    borderColor: "rgba(0, 0, 0, 0.05)",
  },
  anthropomorphismIntroHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: Layout.spacing.sm,
  },
  anthropomorphismIntroIcon: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Layout.spacing.sm,
  },
  anthropomorphismIntroTitle: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
  },
  anthropomorphismIntroDescription: {
    fontSize: Typography.fontSize.sm,
    lineHeight: 20,
    marginBottom: Layout.spacing.base,
  },
  anthropomorphismIntroFeatures: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  anthropomorphismIntroFeature: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
    justifyContent: "center",
  },
  anthropomorphismIntroFeatureText: {
    fontSize: Typography.fontSize.xs,
    marginLeft: Layout.spacing.xs,
    fontWeight: Typography.fontWeight.medium,
  },
});
