import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, Container } from "@/components";
import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Image } from "expo-image";
import { Text, TouchableOpacity, View } from "react-native";

// Mock data for AI beautification items
const beautificationItems = [
  {
    id: "1",
    title: "奶奶的嫁妆盒",
    coverImage: "https://images.unsplash.com/photo-1563203369-26f2e4a5ccf7",
    type: "3D建模",
    icon: "cube",
    status: "completed",
    progress: 100,
  },
  {
    id: "2",
    title: "爷爷的老怀表",
    coverImage: "https://images.unsplash.com/photo-1577083552431-6e5fd01aa342",
    type: "2D美化",
    icon: "sparkles",
    status: "processing",
    progress: 75,
  },
  {
    id: "3",
    title: "外婆的缝纫机",
    coverImage: "https://images.unsplash.com/photo-1551721434-8b94ddff0e6d",
    type: "背景移除",
    icon: "layers",
    status: "pending",
    progress: 0,
  },
  {
    id: "4",
    title: "父亲的老相机",
    coverImage: "https://images.unsplash.com/photo-1503095396549-807759245b35",
    type: "色彩增强",
    icon: "color-palette",
    status: "completed",
    progress: 100,
  },
  {
    id: "5",
    title: "妈妈的首饰盒",
    coverImage: "https://images.unsplash.com/photo-1515562141207-7a88fb7ce338",
    type: "细节修复",
    icon: "construct",
    status: "processing",
    progress: 45,
  },
  {
    id: "6",
    title: "爷爷的钢笔",
    coverImage: "https://images.unsplash.com/photo-1583485088034-697b5bc54ccd",
    type: "3D建模",
    icon: "cube",
    status: "pending",
    progress: 0,
  },
];

const categories = [
  { id: "all", title: "全部", count: beautificationItems.length },
  { id: "3d", title: "3D建模", count: beautificationItems.filter(item => item.type === "3D建模").length },
  { id: "2d", title: "2D美化", count: beautificationItems.filter(item => item.type === "2D美化").length },
  { id: "enhance", title: "色彩增强", count: beautificationItems.filter(item => item.type === "色彩增强").length },
  { id: "repair", title: "细节修复", count: beautificationItems.filter(item => item.type === "细节修复").length },
];

export default function AIBeautificationScreen() {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const handleEnhanceItem = (item: any) => {
    console.log("Enhance item:", item.title);
  };

  const handleViewItem = (item: any) => {
    console.log("View item:", item.title);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "#10B981";
      case "processing":
        return "#F59E0B";
      case "pending":
        return "#6B7280";
      default:
        return colors.textMuted;
    }
  };

  const getStatusText = (status: string) => {
    switch (status) {
      case "completed":
        return "已完成";
      case "processing":
        return "处理中";
      case "pending":
        return "等待中";
      default:
        return "未知";
    }
  };

  return (
    <Container style={{ flex: 1, backgroundColor: colors.backgroundApp }} headerProps={{ title: "AI美化" }} enableScroll>
      <View style={styles.content}>
        {/* Categories */}
        <View style={styles.categoriesContainer}>
          {categories.map(category => (
            <TouchableOpacity
              key={category.id}
              style={[
                styles.categoryChip,
                {
                  backgroundColor: category.id === "all" ? colors.primary : colors.backgroundTertiary,
                },
              ]}
            >
              <Text
                style={[
                  styles.categoryText,
                  {
                    color: category.id === "all" ? colors.textInverse : colors.text,
                  },
                ]}
              >
                {category.title} ({category.count})
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        {/* Items Grid */}
        <View style={styles.itemsGrid}>
          {beautificationItems.map(item => (
            <View key={item.id} style={[styles.itemCard, { backgroundColor: colors.background }]}>
              <View style={styles.itemImage}>
                <Image source={{ uri: item.coverImage }} style={styles.cardImage} />
                <View style={[styles.statusBadge, { backgroundColor: getStatusColor(item.status) }]}>
                  <Text style={styles.statusText}>{getStatusText(item.status)}</Text>
                </View>
              </View>

              <View style={styles.itemContent}>
                <Text style={[styles.itemTitle, { color: colors.text }]} numberOfLines={1}>
                  {item.title}
                </Text>

                <AIBadge text={item.type} icon={item.icon} variant="small" />

                {item.status === "processing" && (
                  <View style={styles.progressContainer}>
                    <View style={[styles.progressBar, { backgroundColor: colors.borderLight }]}>
                      <View
                        style={[
                          styles.progressFill,
                          {
                            backgroundColor: colors.primary,
                            width: `${item.progress}%`,
                          },
                        ]}
                      />
                    </View>
                    <Text style={[styles.progressText, { color: colors.textMuted }]}>{item.progress}%</Text>
                  </View>
                )}

                <View style={styles.itemActions}>{item.status === "completed" ? <Button title="查看结果" variant="primary" size="small" onPress={() => handleViewItem(item)} style={styles.actionButton} /> : item.status === "pending" ? <Button title="开始美化" variant="outline" size="small" onPress={() => handleEnhanceItem(item)} style={styles.actionButton} /> : <Button title="查看进度" variant="ghost" size="small" onPress={() => handleViewItem(item)} style={styles.actionButton} />}</View>
              </View>
            </View>
          ))}
        </View>
      </View>
    </Container>
  );
}

const styles = StyleSheetCreate({
  content: {
    padding: Layout.spacing.base,
    marginBottom: Layout.spacing["2xl"],
  },
  categoriesContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: Layout.spacing.sm,
    marginBottom: Layout.spacing.xl,
  },
  categoryChip: {
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.full,
  },
  categoryText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
  itemsGrid: {
    flexDirection: "row",
    flexWrap: "wrap",
    gap: "2%",
    justifyContent: "space-between",
  },
  itemCard: {
    width: "48%",
    borderRadius: Layout.borderRadius.lg,
    overflow: "hidden",
    marginBottom: Layout.spacing.base,
    ...Layout.shadows.sm,
  },
  itemImage: {
    position: "relative",
    height: 120,
  },
  cardImage: {
    width: "100%",
    height: "100%",
  },
  statusBadge: {
    position: "absolute",
    top: Layout.spacing.sm,
    right: Layout.spacing.sm,
    paddingHorizontal: Layout.spacing.sm,
    paddingVertical: Layout.spacing.xs,
    borderRadius: Layout.borderRadius.sm,
  },
  statusText: {
    color: "white",
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.medium,
  },
  itemContent: {
    padding: Layout.spacing.base,
  },
  itemTitle: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    marginBottom: Layout.spacing.sm,
  },
  progressContainer: {
    marginTop: Layout.spacing.sm,
    marginBottom: Layout.spacing.sm,
  },
  progressBar: {
    height: 4,
    borderRadius: 2,
    overflow: "hidden",
    marginBottom: Layout.spacing.xs,
  },
  progressFill: {
    height: "100%",
    borderRadius: 2,
  },
  progressText: {
    fontSize: Typography.fontSize.xs,
    textAlign: "right",
  },
  itemActions: {
    marginTop: Layout.spacing.sm,
  },
  actionButton: {
    width: "100%",
  },
});
