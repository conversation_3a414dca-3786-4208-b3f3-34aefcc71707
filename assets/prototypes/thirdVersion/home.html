<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆博物馆 - 首页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary: #5D8BF4;
            --secondary: #FF7E5D;
        }
        .phone-frame {
            width: 390px;
            height: 844px;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            margin: 0 auto;
            position: relative;
            background: white;
        }
        .status-bar {
            height: 44px;
            background: white;
            display: flex;
            justify-content: space-between;
            padding: 0 1.5rem;
            align-items: center;
            font-weight: 600;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 20;
        }
        .screen-content {
            height: 100%;
            overflow-y: auto;
            padding-top: 44px;
            padding-bottom: 80px;
        }
        .nav-bar {
            height: 80px;
            background: white;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 0.75rem;
        }
        .nav-icon {
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }
        .active {
            color: var(--primary);
        }
        .story-card {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            margin-bottom: 1rem;
        }
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .section-title .more {
            font-size: 0.875rem;
            color: #666;
        }
        .ai-badge {
            background-color: rgba(93, 139, 244, 0.1);
            color: var(--primary);
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            display: inline-flex;
            align-items: center;
        }
        .ai-badge i {
            margin-right: 0.25rem;
        }
        .story-progress {
            height: 4px;
            background: #eee;
            border-radius: 2px;
            overflow: hidden;
        }
        .story-progress-bar {
            height: 100%;
            background: var(--primary);
        }
        .btn-primary {
            background-color: var(--primary);
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            font-weight: 500;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-frame">
        <div class="status-bar">
            <div>9:41</div>
            <div>
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi ml-2"></i>
                <i class="fas fa-battery-full ml-2"></i>
            </div>
        </div>
        <div class="screen-content p-4">
            <div class="flex justify-between items-center mb-6">
                <h1 class="text-xl font-bold">记忆博物馆</h1>
                <div class="flex">
                    <i class="fas fa-bell text-gray-500 mr-4"></i>
                    <i class="fas fa-search text-gray-500"></i>
                </div>
            </div>
            
            <!-- 我的故事 -->
            <div class="mb-6">
                <div class="section-title">
                    <span>我的故事</span>
                    <span class="more">查看全部 <i class="fas fa-chevron-right text-xs"></i></span>
                </div>
                <div class="story-card">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1577083552431-6e5fd01aa342" alt="故事封面" class="w-full h-40 object-cover">
                        <div class="absolute top-3 right-3 bg-white bg-opacity-80 rounded-full p-2">
                            <i class="fas fa-heart text-red-500"></i>
                        </div>
                    </div>
                    <div class="p-3">
                        <h3 class="font-medium mb-1">爷爷的老怀表</h3>
                        <p class="text-xs text-gray-500 mb-2">
                            这是一个关于时间与记忆的故事，爷爷的怀表见证了整个家族的历史...
                        </p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded-full overflow-hidden mr-2">
                                    <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" 
                                         alt="用户" class="w-full h-full object-cover">
                                </div>
                                <span class="text-xs">李明</span>
                            </div>
                            <div class="text-xs text-gray-500">
                                <i class="fas fa-history mr-1"></i> 2天前更新
                            </div>
                        </div>
                    </div>
                </div>
                <div class="story-card">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1563203369-26f2e4a5ccf7" alt="故事封面" class="w-full h-40 object-cover">
                        <div class="absolute top-3 right-3 bg-white bg-opacity-80 rounded-full p-2">
                            <i class="fas fa-heart text-red-500"></i>
                        </div>
                    </div>
                    <div class="p-3">
                        <h3 class="font-medium mb-极简主义
1">奶奶的嫁妆盒</h3>
                        <p class="text-xs text-gray-500 mb-2">
                            这个红漆木盒承载了奶奶年轻时的梦想与希望，里面的每一件物品都...
                        </p>
                        <div class="flex justify-between items-center">
                            <div class="flex items-center">
                                <div class="w-6 h-6 rounded-full overflow-hidden mr-2">
                                    <img src="https://images.unsplash.com/photo-1535713875002-d1d0cf377fde" 
                                         alt="用户" class="w-full h-full object-cover">
                                </div>
                                <span class="text-xs">李明</span>
                            </div>
                            <div class="text-xs text-gray-500">
                                <i class="fas fa-history mr-1"></i> 1周前更新
                            </极简主义
div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 正在创作的故事 -->
            <div class="mb-6">
                <div class="section-title">
                    <span>正在创作的故事</span>
                    <span class="more">查看全部 <i class="fas fa-chevron-right text-xs"></i></span>
                </div>
                <div class="bg-white rounded-xl p-4 mb-3 shadow-sm">
                    <div class="flex items-start mb-2">
                        <div class="w-16 h-16 rounded-lg overflow-hidden mr-3">
                            <img src="https://images.unsplash.com/photo-1551721434-8b94ddff0e6d" 
                                 alt="故事封面" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1">
                            <h3 class="font-medium mb-1">外婆的缝纫机</h3>
                            <div class="ai-badge mb-2">
                                <i class="fas fa-robot"></i> AI协作中
                            </div>
                            <div class="story-progress mb-1">
                                <div class="story-progress-bar" style="width: 65%"></div>
                            </div>
                            <div class="text-xs text-gray-500">
                                已完成 65%
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button class="btn-primary text-sm">继续创作</button>
                    </div>
                </div>
                <div class="bg-white rounded-xl p-4 shadow-sm">
                    <div class="flex items-start mb-2">
                        <div class="w-16 h-16 rounded-lg overflow-hidden mr-3">
                            <img src="https://images.unsplash.com/photo-1503095396549-807759245b35" 
                                 alt="故事封面" class="w-full h-full object-cover">
                        </div>
                        <div class="flex-1">
                            <h3 class="font-medium mb-1">父亲的老相机</h3>
                            <div class="ai-badge mb-2">
                                <i class="fas fa-robot"></i> AI协作中
                            </div>
                            <div class="story-progress mb-1">
                                <div class="story-progress-bar" style="width: 30%"></div>
                            </div>
                            <div class="text-xs text-gray-500">
                                已完成 30%
                            </div>
                        </div>
                    </div>
                    <div class="flex justify-end">
                        <button class="btn-primary text-sm">继续创作</button>
                    </div>
                </div>
            </div>
            
            <!-- AI美化推荐 -->
            <div>
                <div class="section-title">
                    <span>AI美化推荐</span>
                    <span class="more">查看全部 <i class="fas fa-chevron-right text-xs"></i></span>
                </div>
                <div class="flex space-x-3 overflow-x-auto pb-2">
                    <div class="bg-white rounded-xl p-3 shadow-sm min-w-[200px]">
                        <div class="w-full h-24 rounded-lg overflow-hidden mb-2">
                            <img src="https://images.unsplash.com/photo-1563203369-26f2e4a5ccf7" 
                                 alt="故事封面" class="w-full h-full object-cover">
                        </div>
                        <h3 class="font-medium text-sm mb-1">奶奶的嫁妆盒</h3>
                        <div class="flex justify-between">
                            <div class="ai-badge text-xs">
                                <i class="fas fa-cube"></i> 3D建模
                            </div>
                            <button class="text-xs text-blue-500">立即美化</button>
                        </div>
                    </div>
                    <div class="bg-white rounded-xl p-3 shadow-sm min-w-[200px]">
                        <div class="w-full h-24 rounded-lg overflow-hidden mb-2">
                            <img src="https://images.unsplash.com/photo-1577083552431-6e5fd01aa342" 
                                 alt="故事封面" class="w-full h-full object-cover">
                        </div>
                        <h3 class="font-medium text-sm mb-1">爷爷的老怀表</h3>
                        <div class="flex justify-between">
                            <div class="ai-badge text-xs">
                                <i class="fas fa-magic"></i> 2D美化
                            </div>
                            <button class="text-xs text-blue-500">立即美化</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="nav-bar">
            <div class="nav-item active">
                <i class="fas fa-home nav-icon"></i>
                <span>首页</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-book nav-icon"></i>
                <span>故事集</span>
            </div>
            <div class="nav-item">
                <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
                    <i class="fas fa-plus"></i>
                </div>
            </div>
            <div class="nav-item">
                <i class="fas fa-robot nav-icon"></i>
                <span>AI</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-user nav-icon"></i>
                <span>我的</span>
            </div>
        </div>
    </div>
</body>
</html>
