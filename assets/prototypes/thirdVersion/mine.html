<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>记忆博物馆 - 我的</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      :root {
        --primary: #5d8bf4;
        --secondary: #ff7e5d;
      }
      .phone-frame {
        width: 390px;
        height: 844px;
        border-radius: 40px;
        overflow: hidden;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        margin: 0 auto;
        position: relative;
        background: white;
      }
      .status-bar {
        height: 44px;
        background: white;
        display: flex;
        justify-content: space-between;
        padding: 0 1.5rem;
        align-items: center;
        font-weight: 600;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 20;
      }
      .screen-content {
        height: 100%;
        overflow-y: auto;
        padding-top: 44px;
        padding-bottom: 80px;
      }
      .nav-bar {
        height: 80px;
        background: white;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #eee;
      }
      .nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.75rem;
      }
      .nav-icon {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
      }
      .active {
        color: var(--primary);
      }
      .premium-badge {
        background: linear-gradient(45deg, #ffd700, #ff7e5d);
        color: white;
        border-radius: 12px;
        padding: 0.25rem 0.75rem;
        font-size: 0.75rem;
        font-weight: 500;
      }
      .stat-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
        padding: 1rem;
        text-align: center;
      }
      .grid-3-col {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 1rem;
      }
      .grid-item {
        background: #f5f7ff;
        border-radius: 12px;
        padding: 1rem;
        text-align: center;
      }
      .grid-icon {
        font-size: 1.5rem;
        color: var(--primary);
        margin-bottom: 0.5rem;
      }
    </style>
  </head>
  <body class="bg-gray-100">
    <div class="phone-frame">
      <div class="status-bar">
        <div>9:41</div>
        <div>
          <i class="fas fa-signal"></i>
          <i class="fas fa-wifi ml-2"></i>
          <i class="fas fa-battery-full ml-2"></i>
        </div>
      </div>
      <div class="screen-content p-4">
        <!-- 用户信息 -->
        <div class="flex items-center mb-8">
          <div class="relative mr-4">
            <img src="https://randomuser.me/api/portraits/women/68.jpg" class="w-16 h-16 rounded-full object-cover border-2 border-white shadow" />
            <div class="absolute bottom-0 right-0 bg-blue-500 text-white w-6 h-6 rounded-full flex items-center justify-center">
              <i class="fas fa-pencil-alt text-xs"></i>
            </div>
          </div>
          <div class="flex-1">
            <h1 class="text-xl font-bold">张小雨</h1>
            <p class="text-gray-500 text-sm">收藏家 · 历史爱好者</p>
          </div>
          <div class="premium-badge">
            <i class="fas fa-crown mr-1"></i>
            高级会员
          </div>
        </div>

        <!-- 数据统计 -->
        <div class="grid grid-cols-3 gap-3 mb-6">
          <div class="stat-card">
            <div class="text-2xl font-bold text-blue-500">42</div>
            <div class="text-xs text-gray-500">收藏物品</div>
          </div>
          <div class="stat-card">
            <div class="text-2xl font-bold text-blue-500">18</div>
            <div class="text-xs text-gray-500">创作故事</div>
          </div>
          <div class="stat-card">
            <div class="text-2xl font-bold text-blue-500">127</div>
            <div class="text-xs text-gray-500">协作贡献</div>
          </div>
        </div>

        <!-- 功能网格 -->
        <div class="grid-3-col mb-6">
          <div class="grid-item">
            <div class="grid-icon">
              <i class="fas fa-cloud-upload-alt"></i>
            </div>
            <div class="text-xs">备份数据</div>
          </div>
          <div class="grid-item">
            <div class="grid-icon">
              <i class="fas fa-shield-alt"></i>
            </div>
            <div class="text-xs">隐私设置</div>
          </div>
          <div class="grid-item">
            <div class="grid-icon">
              <i class="fas fa-star"></i>
            </div>
            <div class="text-xs">收藏故事</div>
          </div>
          <div class="grid-item">
            <div class="grid-icon">
              <i class="fas fa-history"></i>
            </div>
            <div class="text-xs">编辑历史</div>
          </div>
          <div class="grid-item">
            <div class="grid-icon">
              <i class="fas fa-question-circle"></i>
            </div>
            <div class="text-xs">帮助中心</div>
          </div>
          <div class="grid-item">
            <div class="grid-icon">
              <i class="fas fa-crown"></i>
            </div>
            <div class="text-xs">会员中心</div>
          </div>
        </div>

        <div class="bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl p-4 mb-6">
          <div class="flex justify-between items-center mb-2">
            <div class="text-white font-medium">高级会员</div>
            <div class="bg-white bg-opacity-20 text-white text-xs px-2 py-1 rounded-full">剩余30天</div>
          </div>
          <div class="text-white text-opacity-80 text-xs mb-3">解锁全部AI功能，无限创作故事</div>
          <button class="bg-white text-blue-500 text-sm py-1 px-3 rounded-lg">续费会员</button>
        </div>
      </div>

      <div class="nav-bar">
        <div class="nav-item">
          <i class="fas fa-home nav-icon"></i>
          <span>首页</span>
        </div>
        <div class="nav-item">
          <i class="fas fa-book nav-icon"></i>
          <span>故事集</span>
        </div>
        <div class="nav-item">
          <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
            <i class="fas fa-plus"></i>
          </div>
        </div>
        <div class="nav-item">
          <i class="fas fa-robot nav-icon"></i>
          <span>AI</span>
        </div>
        <div class极简主义="nav-item active">
          <i class="fas fa-user nav-icon"></i>
          <span>我的</span>
        </div>
      </div>
    </div>
  </body>
</html>
