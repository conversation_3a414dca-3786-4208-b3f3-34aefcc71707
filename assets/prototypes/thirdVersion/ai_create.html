<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆博物馆 - 故事创作</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary: #5D8BF4;
            --secondary: #FF7E5D;
        }
        .phone-frame {
            width: 390px;
            height: 844px;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            margin: 0 auto;
            position: relative;
            background: white;
        }
        .status-bar {
            height: 44px;
            background: white;
            display: flex;
            justify-content: space-between;
            padding: 0 1.5rem;
            align-items: center;
            font-weight: 600;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 20;
        }
        .screen-content {
            height: 100%;
            overflow-y: auto;
            padding-top: 44px;
            padding-bottom: 80px;
        }
        .nav-bar {
            height: 80px;
            background: white;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 0.75rem;
        }
        .nav-icon {
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }
        .active {
            color: var(--primary);
        }
        .story-card {
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .story-checkbox {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            border: 2px solid #ddd;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .story-checkbox.checked {
            background-color: var(--primary);
            border-color: var(--primary);
            color: white;
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-frame">
        <div class="status-bar">
            <div>9:41</div>
            <div>
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi ml-1"></i>
                <i class="fas fa-battery-full ml-1"></i>
            </div>
        </div>
        <div class="screen-content">
            <div class="p-4">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold">故事创作</h1>
                    <div class="text-blue-500">
                        <i class="fas fa-question-circle"></i>
                    </div>
                </div>
                
                <div class="bg-blue-50 p-4 rounded-lg mb-6">
                    <p class="text-sm text-blue-800">
                        选择您想要串联的故事，AI将帮您分析故事之间的关联，创建一个全新的叙事。
                    </p>
                </div>
                
                <!-- 故事选择列表 -->
                <h2 class="text-lg font-bold mb-4">选择故事</h2>
                <div class="space-y-4 mb-8">
                    <div class="story-card bg-white flex">
                        <div class="w-1/3">
                            <img src="https://via.placeholder.com/150x150" alt="故事封面" class="w-full h-full object-cover">
                        </div>
                        <div class="p-3 flex-1 flex justify-between items-center">
                            <div>
                                <h3 class="font-bold">北京之行</h3>
                                <p class="text-gray-500 text-xs">2023年5月10日</p>
                                <div class="flex items-center mt-1">
                                    <i class="fas fa-image text-gray-400 text-xs"></i>
                                    <span class="text-xs text-gray-500 ml-1">12张照片</span>
                                </div>
                            </div>
                            <div class="story-checkbox checked">
                                <i class="fas fa-check text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="story-card bg-white flex">
                        <div class="w-1/3">
                            <img src="https://via.placeholder.com/150x150" alt="故事封面" class="w-full h-full object-cover">
                        </div>
                        <div class="p-3 flex-1 flex justify-between items-center">
                            <div>
                                <h3 class="font-bold">上海旅游</h3>
                                <p class="text-gray-500 text-xs">2023年6月15日</p>
                                <div class="flex items-center mt-1">
                                    <i class="fas fa-image text-gray-400 text-xs"></i>
                                    <span class="text-xs text-gray-500 ml-1">18张照片</span>
                                </div>
                            </div>
                            <div class="story-checkbox checked">
                                <i class="fas fa-check text-xs"></i>
                            </div>
                        </div>
                    </div>
                    
                    <div class="story-card bg-white flex">
                        <div class="w-1/3">
                            <img src="https://via.placeholder.com/150x150" alt="故事封面" class="w-full h-full object-cover">
                        </div>
                        <div class="p-3 flex-1 flex justify-between items-center">
                            <div>
                                <h3 class="font-bold">杭州西湖</h3>
                                <p class="text-gray-500 text-xs">2023年7月5日</p>
                                <div class="flex items-center mt-1">
                                    <i class="fas fa-image text-gray-400 text-xs"></i>
                                    <span class="text-xs text-gray-500 ml-1">9张照片</span>
                                </div>
                            </div>
                            <div class="story-checkbox">
                                <i class="fas fa-check text-xs opacity-0"></i>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 创作方式选择 -->
                <h2 class="text-lg font-bold mb-4">创作方式</h2>
                <div class="grid grid-cols-2 gap-4 mb-8">
                    <div class="bg-white rounded-lg p-4 border-2 border-blue-500">
                        <div class="flex justify-between items-start mb-2">
                            <div class="bg-blue-100 p-2 rounded-full">
                                <i class="fas fa-file-alt text-blue-500"></i>
                            </div>
                            <div class="story-checkbox checked">
                                <i class="fas fa-check text-xs"></i>
                            </div>
                        </div>
                        <h3 class="font-bold">文字记录</h3>
                        <p class="text-xs text-gray-500 mt-1">
                            AI分析故事关联，生成文字叙事并提供建议
                        </p>
                    </div>
                    
                    <div class="bg-white rounded-lg p-4">
                        <div class="flex justify-between items-start mb-2">
                            <div class="bg-purple-100 p-2 rounded-full">
                                <i class="fas fa-paint-brush text-purple-500"></i>
                            </div>
                            <div class="story-checkbox">
                                <i class="fas fa-check text-xs opacity-0"></i>
                            </div>
                        </div>
                        <h3 class="font-bold">画面记录</h3>
                        <p class="text-xs text-gray-500 mt-1">
                            以漫画形式绘制故事，创建视觉叙事
                        </p>
                    </div>
                </div>
                
                <!-- 创建按钮 -->
                <button class="w-full bg-blue-500 text-white py-3 rounded-lg font-bold">
                    开始创作
                </button>
            </div>
        </div>
        <div class="nav-bar">
            <div class="nav-item">
                <i class="fas fa-home nav-icon"></i>
                <span>首页</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-book nav-icon"></i>
                <span>故事集</span>
            </div>
            <div class="nav-item">
                <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
                    <i class="fas fa-plus"></i>
                </div>
            </div>
            <div class="nav-item active">
                <i class="fas fa-robot nav-icon"></i>
                <span>AI</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-user nav-icon"></i>
                <span>我的</span>
            </div>
        </div>
    </div>
</body>
</html>
