<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆博物馆 - 故事详情</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <style>
        :root {
            --primary: #5D8BF4;
            --secondary: #FF7E5D;
        }
        .phone-frame {
            width: 390px;
            height: 844px;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            margin: 0 auto;
            position: relative;
            background: white;
        }
        .status-bar {
            height: 44px;
            background: white;
            display: flex;
            justify-content: space-between;
            padding: 0 1.5rem;
            align-items: center;
            font-weight: 600;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 20;
        }
        .screen-content {
            height: 100%;
            overflow-y: auto;
            padding-top: 44px;
            padding-bottom: 80px;
        }
        .nav-bar {
            height: 80px;
            background: white;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
        }
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 0.75rem;
        }
        .nav-icon {
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }
        .active {
            color: var(--primary);
        }
        .story-header {
            position: relative;
            height: 240px;
        }
        .story-cover {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .story-header-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
            padding: 1rem;
            color: white;
        }
        .story-tag {
            background-color: rgba(255,255,255,0.2);
            color: white;
            padding: 0.25rem 0.5rem;
            border-radius: 4px;
            font-size: 0.75rem;
            margin-right: 0.5rem;
        }
        .story-action-btn {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .timeline-item {
            position: relative;
            padding-left: 2rem;
            padding-bottom: 1.5rem;
        }
        .timeline-item:before {
            content: '';
            position: absolute;
            left: 0.5rem;
            top: 0.5rem;
            width: 1rem;
            height: 1rem;
            border-radius: 50%;
            background: var(--primary);
        }
        .timeline-item:after {
            content: '';
            position: absolute;
            left: 0.9rem;
            top: 1.5rem;
            width: 2px;
            height: calc(100% - 1.5rem);
            background: #e5e7eb;
        }
        .timeline-item:last-child:after {
            display: none;
        }
        .timeline-date {
            font-size: 0.875rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }
        .timeline-content {
            background: #f9fafb;
            border-radius: 0.5rem;
            padding: 1rem;
        }
        .model-viewer {
            width: 100%;
            height: 200px;
            background: #f1f5f9;
            border-radius: 0.5rem;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        .model-controls {
            position: absolute;
            bottom: 0.5rem;
            right: 0.5rem;
            display: flex;
        }
        .model-btn {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-left: 0.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .section-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: 1rem;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .section-title .more {
            font-size: 0.875rem;
            color: #666;
        }
        .image-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 0.5rem;
            margin-bottom: 1rem;
        }
        .image-grid-item {
            aspect-ratio: 1/1;
            overflow: hidden;
            border-radius: 0.5rem;
            position: relative;
        }
        .image-grid-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .image-count {
            position: absolute;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.6);
            color: white;
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-top-left-radius: 0.5rem;
        }
        .ai-feature-card {
            background: #f0f4ff;
            border-radius: 1rem;
            padding: 1rem;
            margin-bottom: 1rem;
            border: 1px solid #dbe4ff;
            transition: all 0.2s;
        }
        .ai-feature-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
        .ai-feature-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 1rem;
            color: var(--primary);
            font-size: 1.25rem;
            box-shadow: 0 2px 4px rgba(93, 139, 244, 0.1);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-frame">
        <div class="status-bar">
            <div>9:41</div>
            <div>
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi ml-2"></i>
                <i class="fas fa-battery-full ml-2"></i>
            </div>
        </div>
        <div class="screen-content">
            <!-- 故事头部 -->
            <div class="story-header">
                <img src="https://images.unsplash.com/photo-1577083552431-6e5fd01aa342" alt="故事封面" class="story-cover">
                <div class="absolute top-4 left-4">
                    <button class="story-action-btn">
                        <i class="fas fa-arrow-left text-gray-600"></i>
                    </button>
                </div>
                <div class="absolute top-4 right-4 flex space-x-2">
                    <button class="story-action-btn">
                        <i class="fas fa-edit text-gray-600"></i>
                    </button>
                    <button class="story-action-btn">
                        <i class="fas fa-share-alt text-gray-600"></i>
                    </button>
                </div>
                <div class="story-header-overlay">
                    <h1 class="text-xl font-bold mb-2">爷爷的老怀表</h1>
                    <div class="flex mb-3">
                        <span class="story-tag"><i class="fas fa-clock mr-1"></i> 1950年代</span>
                        <span class="story-tag"><i class="fas fa-tag mr-1"></i> 家族记忆</span>
                    </div>
                    <div class="text-xs opacity-80">2023年10月15日创建</div>
                </div>
            </div>
            
            <!-- 故事简介 -->
            <div class="p-4 bg-white mb-2">
                <p class="text-gray-700">
                    这是一个关于时间与记忆的故事，爷爷的怀表见证了整个家族的历史变迁。这个黄铜怀表从1920年代一直陪伴着我们家族，经历了战争、迁徙和团聚...
                </p>
            </div>
            
            <!-- 图片展示区 -->
            <div class="p-4 bg-white mb-2">
                <h2 class="section-title">
                    <span>故事图片</span>
                    <span class="more text-primary">全部 (9) <i class="fas fa-chevron-right text-xs"></i></span>
                </h2>
                
                <div class="image-grid">
                    <div class="image-grid-item">
                        <img src="https://images.unsplash.com/photo-1584208124218-703c2dbc0c0c" alt="老照片">
                    </div>
                    <div class="image-grid-item">
                        <img src="https://images.unsplash.com/photo-1509048191080-d2984bad6ae5" alt="怀表照片">
                    </div>
                    <div class="image-grid-item">
                        <img src="https://images.unsplash.com/photo-1558697698-9300a84a0fda" alt="历史照片">
                    </div>
                    <div class="image-grid-item">
                        <img src="https://images.unsplash.com/photo-1589491106922-a8e488665d5c" alt="现代照片">
                    </div>
                    <div class="image-grid-item">
                        <img src="https://images.unsplash.com/photo-1551721434-8b94ddff0e6d" alt="家族照片">
                    </div>
                    <div class="image-grid-item">
                        <img src="https://images.unsplash.com/photo-1577083552431-6e5fd01aa342" alt="怀表特写">
                    </div>
                    <div class="image-grid-item">
                        <img src="https://images.unsplash.com/photo-1584208124218-703c2dbc0c0c" alt="老照片">
                    </div>
                    <div class="image-grid-item">
                        <img src="https://images.unsplash.com/photo-1509048191080-d2984bad6ae5" alt="怀表照片">
                    </div>
                    <div class="image-grid-item relative">
                        <img src="https://images.unsplash.com/photo-1558697698-9300a84a0fda" alt="历史照片" class="opacity-80">
                        <div class="image-count">+3</div>
                    </div>
                </div>
            </div>
            
            <!-- 故事时间线 -->
            <div class="p-4 bg-white mb-2">
                <h2 class="section-title">
                    <span>故事时间线</span>
                </h2>
                
                <div class="timeline-item">
                    <div class="timeline-date">1925年</div>
                    <div class="timeline-content">
                        <h3 class="font-medium mb-2">怀表的来历</h3>
                        <p class="text-sm text-gray-700 mb-3">
                            爷爷20岁那年，从上海的一家钟表店买下了这块英国产的怀表，花了他半年的积蓄。那时他刚开始在银行工作，这是他给自己的第一份重要礼物。
                        </p>
                        
                        <!-- 九宫格图片布局 -->
                        <div class="image-grid">
                            <div class="image-grid-item">
                                <img src="https://images.unsplash.com/photo-1584208124218-703c2dbc0c0c" alt="老照片">
                            </div>
                            <div class="image-grid-item">
                                <img src="https://images.unsplash.com/photo-1509048191080-d2984bad6ae5" alt="怀表照片">
                            </div>
                            <div class="image-grid-item">
                                <img src="https://images.unsplash.com/photo-1558697698-9300a84a0fda" alt="历史照片">
                            </div>
                        </div>
                        
                        <div class="model-viewer mt-3">
                            <img src="https://images.unsplash.com/photo-1509048191080-d2984bad6ae5" 
                                 alt="怀表3D模型预览" class="w-1/2 h-auto">
                            <div class="absolute top-2 left-2 bg-blue-100 text-blue-600 px-2 py-1 rounded-md text-xs flex items-center">
                                <i class="fas fa-cube mr-1"></i> 3D模型
                            </div>
                            <div class="model-controls">
                                <button class="model-btn">
                                    <i class="fas fa-expand text-gray-600 text-xs"></i>
                                </button>
                                <button class="model-btn">
                                    <i class="fas fa-sync text-gray-600 text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-date">1937年</div>
                    <div class="timeline-content">
                        <h3 class="font-medium mb-2">战争中的守护</h3>
                        <p class="text-sm text-gray-700 mb-3">
                            抗日战争爆发后，爷爷带着全家从上海逃往重庆。在慌乱的逃亡中，这块怀表一直贴身携带，成为他坚持下去的信念。
                        </p>
                        
                        <!-- 九宫格图片布局 -->
                        <div class="image-grid">
                            <div class="image-grid-item">
                                <img src="https://images.unsplash.com/photo-1558697698-9300a84a0fda" alt="历史照片">
                            </div>
                            <div class="image-grid-item">
                                <img src="https://images.unsplash.com/photo-1589491106922-a8e488665d5c" alt="现代照片">
                            </div>
                        </div>
                        
                        <div class="flex items-center justify-between mt-3 bg-blue-50 p-2 rounded-lg">
                            <div class="flex items-center text-blue-600 text-sm">
                                <i class="fas fa-volume-up mr-2"></i> 语音讲述
                            </div>
                            <button class="bg-white text-primary text-sm px-3 py-1 rounded-full shadow-sm">
                                <i class="fas fa-play mr-1"></i> 播放
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-date">1950年</div>
                    <div class="timeline-content">
                        <h3 class="font-medium mb-2">新中国的曙光</h3>
                        <p class="text-sm text-gray-700">
                            新中国成立后，爷爷回到上海，重新开始生活。怀表被小心地保存在木盒中，每逢重要日子才拿出来使用。
                        </p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-date">1985年</div>
                    <div class="timeline-content">
                        <h3 class="font-medium mb-2">传承给父亲</h3>
                        <p class="text-sm text-gray-700">
                            爷爷在我父亲40岁生日那天，将怀表郑重地交给了他，希望这份家族记忆能够继续传承下去。
                        </p>
                    </div>
                </div>
                
                <div class="timeline-item">
                    <div class="timeline-date">2020年</div>
                    <div class="timeline-content">
                        <h3 class="font-medium mb-2">传到我的手中</h3>
                        <p class="text-sm text-gray-700 mb-3">
                            在我30岁生日那天，父亲将这块已有近百年历史的怀表交给了我。现在，它成为了连接我与家族历史的重要纽带。
                        </p>
                        
                        <!-- 九宫格图片布局 -->
                        <div class="image-grid">
                            <div class="image-grid-item">
                                <img src="https://images.unsplash.com/photo-1589491106922-a8e488665d5c" alt="现代照片">
                            </div>
                            <div class="image-grid-item">
                                <img src="https://images.unsplash.com/photo-1509048191080-d2984bad6ae5" alt="怀表现状">
                            </div>
                            <div class="image-grid-item">
                                <img src="https://images.unsplash.com/photo-1551721434-8b94ddff0e6d" alt="家族照片">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- AI增强功能区 -->
            <div class="p-4 bg-white mb-2">
                <h2 class="section-title">
                    <span>AI增强功能</span>
                    <span class="more text-primary">更多 <i class="fas fa-chevron-right text-xs"></i></span>
                </h2>
                
                <div class="flex flex-col space-y-3">
                    <!-- 文本润色功能 -->
                    <div class="ai-feature-card">
                        <div class="flex items-start">
                            <div class="ai-feature-icon">
                                <i class="fas fa-magic"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-medium text-sm mb-1">文本润色</h3>
                                <p class="text-xs text-gray-700 mb-2">
                                    AI可以帮您优化故事文本，提升表达效果，使故事更加生动感人。
                                </p>
                                <div class="flex space-x-2">
                                    <button class="bg-primary text-white text-xs px-3 py-1 rounded-full">
                                        应用
                                    </button>
                                    <button class="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full">
                                        预览效果
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 历史背景补充 -->
                    <div class="ai-feature-card">
                        <div class="flex items-start">
                            <div class="ai-feature-icon">
                                <i class="fas fa-book-open"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-medium text-sm mb-1">历史背景补充</h3>
                                <p class="text-xs text-gray-700 mb-2">
                                    AI可以帮您补充1920-1950年代上海的历史背景，让故事更加丰富。
                                </p>
                                <div class="flex space-x-2">
                                    <button class="bg-primary text-white text-xs px-3 py-1 rounded-full">
                                        应用
                                    </button>
                                    <button class="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full">
                                        预览效果
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 图片修复增强 -->
                    <div class="ai-feature-card">
                        <div class="flex items-start">
                            <div class="ai-feature-icon">
                                <i class="fas fa-image"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-medium text-sm mb-1">老照片修复增强</h3>
                                <p class="text-xs text-gray-700 mb-2">
                                    AI可以修复泛黄、褪色或有划痕的老照片，提升照片质量和清晰度。
                                </p>
                                <div class="flex space-x-2">
                                    <button class="bg-primary text-white text-xs px-3 py-1 rounded-full">
                                        选择照片
                                    </button>
                                    <button class="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full">
                                        查看示例
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 语音朗读生成 -->
                    <div class="ai-feature-card">
                        <div class="flex items-start">
                            <div class="ai-feature-icon">
                                <i class="fas fa-microphone-alt"></i>
                            </div>
                            <div class="flex-1">
                                <h3 class="font-medium text-sm mb-1">语音朗读生成</h3>
                                <p class="text-xs text-gray-700 mb-2">
                                    AI可以将您的故事转换为自然流畅的语音朗读，支持多种音色选择。
                                </p>
                                <div class="flex space-x-2">
                                    <button class="bg-primary text-white text-xs px-3 py-1 rounded-full">
                                        生成语音
                                    </button>
                                    <button class="bg-gray-100 text-gray-700 text-xs px-3 py-1 rounded-full">
                                        试听
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 底部导航栏 -->
        <div class="nav-bar">
            <div class="nav-item">
                <i class="fas fa-home nav-icon"></i>
                <span>首页</span>
            </div>
            <div class="nav-item active">
                <i class="fas fa-book nav-icon"></i>
                <span>故事集</span>
            </div>
            <div class="nav-item">
                <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
                    <i class="fas fa-plus"></i>
                </div>
            </div>
            <div class="nav-item">
                <i class="fas fa-robot nav-icon"></i>
                <span>AI</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-user nav-icon"></i>
                <span>我的</span>
            </div>
        </div>
    </div>
</body>
</html>
