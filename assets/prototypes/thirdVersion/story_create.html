<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>记忆博物馆 - 原型展示</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      :root {
        --primary: #5d8bf4;
        --secondary: #ff7e5d;
      }
      body {
        font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
      }
      .phone-frame {
        width: 390px;
        height: 844px;
        border-radius: 40px;
        overflow: hidden;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        margin: 2rem;
        position: relative;
        background: white;
      }
      .status-bar {
        height: 44px;
        background: white;
        display: flex;
        justify-content: space-between;
        padding: 0 1.5rem;
        align-items: center;
        font-weight: 600;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 20;
      }
      .screen-content {
        height: 100%;
        overflow-y: auto;
        padding-top: 44px;
        padding-bottom: 80px;
      }
      .nav-bar {
        height: 80px;
        background: white;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #eee;
      }
      .nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.75rem;
      }
      .nav-icon {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
      }
      .active {
        color: var(--primary);
      }
      .story-card {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
      }
      .section-title {
        font-size: 1.25rem;
        font-weight: 600;
        margin-bottom: 1rem;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      .section-title .more {
        font-size: 0.875rem;
        color: #666;
      }
      .tab-content {
        display: none;
      }
      .tab-content.active {
        display: block;
      }
      .ai-badge {
        background-color: rgba(93, 139, 244, 0.1);
        color: var(--primary);
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        display: inline-flex;
        align-items: center;
      }
      .ai-badge i {
        margin-right: 0.25rem;
      }
      .story-progress {
        height: 4px;
        background: #eee;
        border-radius: 2px;
        overflow: hidden;
      }
      .story-progress-bar {
        height: 100%;
        background: var(--primary);
      }
      .btn-primary {
        background-color: var(--primary);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 500;
      }
      .btn-outline {
        border: 1px solid #ddd;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 500;
      }
      .avatar {
        width: 40px;
        height: 40px;
        border-radius: 50%;
        overflow: hidden;
      }
      .avatar img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .tab-bar {
        display: flex;
        border-bottom: 1px solid #eee;
        margin-bottom: 1rem;
      }
      .tab-item {
        padding: 0.75rem 1rem;
        font-size: 0.875rem;
        position: relative;
      }
      .tab-item.active {
        color: var(--primary);
        font-weight: 500;
      }
      .tab-item.active:after {
        content: "";
        position: absolute;
        bottom: -1px;
        left: 25%;
        width: 50%;
        height: 2px;
        background: var(--primary);
        border-radius: 2px;
      }
      .editor-toolbar {
        display: flex;
        justify-content: space-around;
        padding: 0.5rem;
        border-top: 1px solid #eee;
        background: white;
      }
      .toolbar-item {
        font-size: 1.25rem;
        color: #666;
        padding: 0.5rem;
      }
      .timeline-item {
        display: flex;
        margin-bottom: 1rem;
      }
      .timeline-line {
        width: 2px;
        background: #eee;
        margin: 0 1rem;
        position: relative;
      }
      .timeline-dot {
        width: 12px;
        height: 12px;
        border-radius: 50%;
        background: var(--primary);
        position: absolute;
        top: 0;
        left: -5px;
      }
      .timeline-content {
        flex: 1;
      }
      .feature-card {
        background: #f9f9f9;
        border-radius: 12px;
        padding: 1rem;
        margin-bottom: 1rem;
        display: flex;
        align-items: center;
      }
      .feature-icon {
        width: 48px;
        height: 48px;
        border-radius: 12px;
        background: rgba(93, 139, 244, 0.1);
        display: flex;
        align-items: center;
        justify-content: center;
        color: var(--primary);
        font-size: 1.5rem;
        margin-right: 1rem;
      }
      .feature-content {
        flex: 1;
      }
      .feature-title {
        font-weight: 500;
        margin-bottom: 0.25rem;
      }
      .feature-desc {
        font-size: 0.75rem;
        color: #666;
      }
      .page-container {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
      }
    </style>
  </head>
  <body class="bg-gray-100">
    <div class="page-container">
      <!-- 故事添加页面 -->
      <div class="phone-frame" id="add-story-page">
        <div class="status-bar">
          <div>9:41</div>
          <div>
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi ml-2"></i>
            <i class="fas fa-battery-full ml-2"></i>
          </div>
        </div>
        <div class="screen-content p-4">
          <div class="flex justify-between items-center mb-6">
            <div class="flex items-center">
              <i class="fas fa-arrow-left mr-4"></i>
              <h1 class="text-xl font-bold">创建新故事</h1>
            </div>
            <button class="btn-primary">保存</button>
          </div>

          <!-- 故事标题 -->
          <div class="mb-6">
            <input type="text" class="w-full p-3 border border-gray-300 rounded-lg" placeholder="请输入故事标题" />
          </div>

          <!-- 故事正文 -->
          <div class="mb-4">
            <textarea class="w-full h-32 border border-gray-200 rounded-lg p-3 text-sm" placeholder="讲述这个物品的故事...">
这是一个来自1940年代的瑞士怀表，是爷爷年轻时在上海工作时购买的。它见证了他的青春岁月，也陪伴他度过了战争年代的艰难时光。

后来，这个怀表成为了我们家族的传家宝，从爷爷传给父亲，再传给我。每一次传承，都会讲述一段家族历史。</textarea
            >
          </div>

          <!-- 故事封面 -->
          <div class="mb-6">
            <label class="block text-sm font-medium text-gray-700 mb-2">故事封面</label>
            <div class="bg-gray-100 rounded-lg h-48 flex items-center justify-center">
              <div class="text-center">
                <i class="fas fa-camera text-3xl text-gray-400 mb-2"></i>
                <div class="text-sm text-gray-500">点击上传封面图片</div>
              </div>
            </div>
          </div>

          <!-- 标签 -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <div class="text-sm font-medium">标签</div>
            </div>
            <div class="flex flex-wrap gap-2">
              <div class="bg-blue-100 text-blue-500 px-2 py-1 rounded-full text-xs flex items-center">
                家族记忆
                <i class="fas fa-times ml-1"></i>
              </div>
              <div class="bg-blue-100 text-blue-500 px-2 py-1 rounded-full text-xs flex items-center">
                老物件
                <i class="fas fa-times ml-1"></i>
              </div>
              <div class="bg-blue-100 text-blue-500 px-2 py-1 rounded-full text-xs flex items-center">
                怀表
                <i class="fas fa-times ml-1"></i>
              </div>
              <div class="border border-dashed border-gray-300 px-2 py-1 rounded-full text-xs text-gray-500">+ 添加标签</div>
            </div>
          </div>

          <!-- 时间 -->
          <div class="mb-4">
            <div class="flex justify-between items-center mb-2">
              <div class="text-sm font-medium">时间</div>
            </div>
            <input type="text" class="w-full border border-gray-200 rounded-lg p-3 text-sm" value="1940年 - 2023年" placeholder="时间范围" />
          </div>

          <!-- 时间线 -->
          <div class="mb-6">
            <div class="flex justify-between items-center mb-2">
              <label class="block text-sm font-medium text-gray-700">时间线</label>
              <button class="text-blue-500 text-sm">
                <i class="fas fa-plus mr-1"></i>
                添加时间点
              </button>
            </div>
            <div class="timeline-item">
              <div class="timeline-line">
                <div class="timeline-dot"></div>
              </div>
              <div class="timeline-content">
                <div class="bg-white p-3 rounded-lg shadow-sm mb-2">
                  <div class="flex justify-between mb-2">
                    <div class="text-sm font-medium">1980年春</div>
                    <i class="fas fa-ellipsis-v text-gray-400"></i>
                  </div>
                  <div class="text-sm text-gray-600">这是爷爷从上海带回来的怀表，当时他刚从大学毕业...</div>
                </div>
                <div class="flex space-x-2">
                  <button class="btn-outline text-xs flex-1">
                    <i class="fas fa-image mr-1"></i>
                    添加图片
                  </button>
                  <button class="btn-outline text-xs flex-1">
                    <i class="fas fa-microphone mr-1"></i>
                    添加语音
                  </button>
                </div>
              </div>
            </div>
            <div class="timeline-item">
              <div class="timeline-line">
                <div class="timeline-dot"></div>
              </div>
              <div class="timeline-content">
                <div class="bg-white p-3 rounded-lg shadow-sm mb-2">
                  <div class="flex justify-between mb-2">
                    <div class="text-sm font-medium">1995年冬</div>
                    <i class="fas fa-ellipsis-v text-gray-400"></i>
                  </div>
                  <div class="text-sm text-gray-600">爷爷把怀表交给了我父亲，作为他30岁生日的礼物...</div>
                </div>
                <div class="flex space-x-2">
                  <button class="btn-outline text-xs flex-1">
                    <i class="fas fa-image mr-1"></i>
                    添加图片
                  </button>
                  <button class="btn-outline text-xs flex-1">
                    <i class="fas fa-microphone mr-1"></i>
                    添加语音
                  </button>
                </div>
              </div>
            </div>
          </div>

          <!-- AI助手 -->
          <div>
            <div class="section-title">
              <span>AI助手</span>
            </div>

            <div class="mb-4">
              <label class="font-medium block mb-2">AI助手</label>
              <div class="bg-blue-50 rounded-xl p-3">
                <div class="flex items-start mb-2">
                  <div class="bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center mr-2">
                    <i class="fas fa-robot text-blue-600"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm">检测到您添加了怀表照片，是否要生成3D模型？</p>
                    <div class="flex mt-2">
                      <button class="btn-primary mr-2 text-sm">生成3D模型</button>
                      <button class="btn-outline text-sm">暂不需要</button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-blue-50 rounded-xl p-4">
              <div class="flex items-center mb-3">
                <div class="w-10 h-10 rounded-full bg-blue-500 flex items-center justify-center text-white mr-3">
                  <i class="fas fa-robot"></i>
                </div>
                <div>
                  <div class="font-medium">AI创作助手</div>
                  <div class="text-xs text-gray-500">让AI帮你完善故事细节</div>
                </div>
              </div>
              <div class="flex space-x-2">
                <button class="btn-primary text-sm flex-1">
                  <i class="fas fa-magic mr-1"></i>
                  AI润色内容
                </button>
                <button class="btn-primary text-sm flex-1">
                  <i class="fas fa-cube mr-1"></i>
                  3D建模
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <script>
      // 这里可以添加交互逻辑
    </script>
  </body>
</html>
