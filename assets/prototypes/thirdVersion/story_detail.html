<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>记忆博物馆 - 故事详情</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.css" />
    <style>
      :root {
        --primary: #5d8bf4;
        --secondary: #ff7e5d;
      }
      body {
        font-family: "PingFang SC", "Helvetica Neue", Arial, sans-serif;
      }
      .phone-frame {
        width: 390px;
        height: 844px;
        border-radius: 40px;
        overflow: hidden;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        margin: 2rem;
        position: relative;
        background: white;
      }
      .status-bar {
        height: 44px;
        background: white;
        display: flex;
        justify-content: space-between;
        padding: 0 1.5rem;
        align-items: center;
        font-weight: 600;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 20;
      }
      .screen-content {
        height: 100%;
        overflow-y: auto;
        padding-top: 44px;
        padding-bottom: 80px;
      }
      .nav-bar {
        height: 80px;
        background: white;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #eee;
      }
      .nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.75rem;
      }
      .nav-icon {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
      }
      .active {
        color: var(--primary);
      }
      .btn-primary {
        background-color: var(--primary);
        color: white;
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 500;
      }
      .btn-outline {
        border: 1px solid var(--primary);
        color: var(--primary);
        padding: 0.5rem 1rem;
        border-radius: 8px;
        font-weight: 500;
      }
      .mode-selector {
        display: inline-flex;
        background-color: #f3f4f6;
        border-radius: 8px;
        padding: 2px;
        margin-bottom: 1rem;
      }
      .mode-option {
        padding: 6px 12px;
        border-radius: 6px;
        font-size: 0.875rem;
        cursor: pointer;
        transition: all 0.2s;
      }
      .mode-option.active {
        background-color: white;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        color: var(--primary);
      }
      .timeline-item {
        position: relative;
        padding-left: 28px;
        padding-bottom: 24px;
      }
      .timeline-item:before {
        content: "";
        position: absolute;
        left: 8px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #e5e7eb;
      }
      .timeline-item:last-child:before {
        bottom: auto;
        height: 8px;
      }
      .timeline-dot {
        position: absolute;
        left: 0;
        top: 6px;
        width: 18px;
        height: 18px;
        border-radius: 50%;
        background-color: var(--primary);
        border: 3px solid white;
        box-shadow: 0 0 0 2px var(--primary);
      }
      .swiper {
        width: 100%;
        height: 240px;
        border-radius: 16px;
        overflow: hidden;
        position: relative;
      }
      .swiper-slide img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }
      .swiper-pagination-bullet-active {
        background-color: var(--primary);
      }
      .fullscreen-btn {
        position: absolute;
        bottom: 15px;
        right: 15px;
        background: rgba(0, 0, 0, 0.5);
        color: white;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 10;
        cursor: pointer;
      }
      .fullscreen-overlay {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-color: rgba(0, 0, 0, 0.9);
        z-index: 100;
        display: none;
        align-items: center;
        justify-content: center;
      }
      .fullscreen-image {
        max-width: 90%;
        max-height: 90%;
      }
      .fullscreen-close {
        position: absolute;
        top: 20px;
        right: 20px;
        color: white;
        font-size: 24px;
        cursor: pointer;
      }
      .ai-assistant-card {
        background: linear-gradient(135deg, #f0f7ff 0%, #e6f0ff 100%);
        border-radius: 16px;
        padding: 16px;
        box-shadow: 0 4px 12px rgba(93, 139, 244, 0.1);
        border: 1px solid rgba(93, 139, 244, 0.2);
      }
      .ai-action-btn {
        background: linear-gradient(135deg, #5d8bf4 0%, #4a7dff 100%);
        color: white;
        padding: 8px 16px;
        border-radius: 8px;
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(93, 139, 244, 0.3);
        transition: all 0.2s;
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .ai-action-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(93, 139, 244, 0.4);
      }
      .ai-action-btn i {
        margin-right: 8px;
      }
      .ai-suggestion {
        background-color: white;
        border-radius: 12px;
        padding: 12px;
        margin-top: 12px;
        box-shadow: 0 2px 6px rgba(0, 0, 0, 0.05);
        border: 1px solid #e5e7eb;
      }
      .ai-avatar {
        background: linear-gradient(135deg, #5d8bf4 0%, #4a7dff 100%);
        width: 40px;
        height: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 2px 8px rgba(93, 139, 244, 0.3);
      }
      .tag-container {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
        margin-top: 8px;
      }
      .tag {
        background-color: #eef4ff;
        color: var(--primary);
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.875rem;
        display: flex;
        align-items: center;
      }
      .tag-remove {
        margin-left: 6px;
        cursor: pointer;
      }
      .add-tag-btn {
        background-color: #f3f4f6;
        color: #6b7280;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.875rem;
        cursor: pointer;
      }
    </style>
  </head>
  <body class="bg-gray-100">
    <div class="phone-frame">
      <div class="status-bar">
        <div>9:41</div>
        <div>
          <i class="fas fa-signal"></i>
          <i class="fas fa-wifi ml-2"></i>
          <i class="fas fa-battery-full ml-2"></i>
        </div>
      </div>
      <div class="screen-content">
        <div class="p-4">
          <!-- 顶部导航 -->
          <div class="flex items-center mb-4">
            <button class="mr-2">
              <i class="fas fa-arrow-left text-gray-700"></i>
            </button>
            <h1 class="text-xl font-bold flex-1">故事详情</h1>
            <button class="ml-2">
              <i class="fas fa-ellipsis-v text-gray-700"></i>
            </button>
          </div>

          <!-- 故事图片画廊 -->
          <div class="mb-4">
            <div class="swiper mySwiper">
              <div class="swiper-wrapper">
                <div class="swiper-slide" onclick="openFullscreen(this)">
                  <img src="https://images.unsplash.com/photo-1577083552431-6e5fd01aa342" alt="故事图片1" />
                </div>
                <div class="swiper-slide" onclick="openFullscreen(this)">
                  <img src="https://images.unsplash.com/photo-1551721434-8b94ddff0e6d" alt="故事图片2" />
                </div>
                <div class="swiper-slide" onclick="openFullscreen(this)">
                  <img src="https://images.unsplash.com/photo-1503095396549-807759245b35" alt="故事图片3" />
                </div>
              </div>
              <div class="swiper-pagination"></div>
              <div class="fullscreen-btn" onclick="openFullscreen(document.querySelector('.swiper-slide-active'))">
                <i class="fas fa-expand"></i>
              </div>
            </div>
            <div class="flex justify-between mt-2">
              <div class="text-sm text-gray-500">3张图片</div>
              <button class="text-sm text-blue-500 flex items-center">
                <i class="fas fa-plus mr-1"></i>
                添加图片
              </button>
            </div>
          </div>

          <!-- 故事标题 -->
          <div class="mb-4">
            <label class="font-medium block mb-2">故事标题</label>
            <input type="text" class="w-full border border-gray-300 rounded-lg p-3" placeholder="请输入故事标题" value="爷爷的老怀表" />
          </div>

          <!-- 内容模式选择器 -->
          <div class="mb-4">
            <label class="font-medium block mb-2">内容填充</label>
            <div class="mode-selector">
              <div class="mode-option active" onclick="switchMode('normal')">普通模式</div>
              <div class="mode-option" onclick="switchMode('timeline')">时间线模式</div>
            </div>

            <!-- 普通模式内容 -->
            <div id="normal-mode" class="mode-content">
              <div class="border border-gray-300 rounded-lg p-3 min-h-[200px]">
                <div class="flex items-center border-b border-gray-200 pb-2 mb-2">
                  <button class="mr-3 text-gray-500"><i class="fas fa-bold"></i></button>
                  <button class="mr-3 text-gray-500"><i class="fas fa-italic"></i></button>
                  <button class="mr-3 text-gray-500"><i class="fas fa-underline"></i></button>
                  <button class="mr-3 text-gray-500"><i class="fas fa-list"></i></button>
                  <button class="mr-3 text-gray-500"><i class="fas fa-image"></i></button>
                </div>
                <div contenteditable="true" class="outline-none min-h-[160px]">爷爷的怀表是一个有着悠久历史的家族传承物品。这个黄铜制的怀表已经有超过100年的历史了，从我的曾祖父开始，一直传到了爷爷手中。 怀表的表面已经有些磨损，但依然能够看清楚罗马数字的时间刻度。表盖上刻有精美的花纹，背面则刻有我们家族的姓氏首字母。 每当爷爷打开这个怀表，总会伴随着一段关于过去的故事。他常说，这个怀表不仅仅是计时的工具，更是家族记忆的载体。</div>
              </div>
            </div>

            <!-- 时间线模式内容 -->
            <div id="timeline-mode" class="mode-content hidden">
              <div class="timeline-container">
                <div class="timeline-item">
                  <div class="timeline-dot"></div>
                  <div class="mb-2">
                    <input type="text" class="w-full border border-gray-300 rounded-lg p-2 text-sm" placeholder="时间节点标题" value="1920年 - 怀表的起源" />
                  </div>
                  <div class="mb-2">
                    <textarea class="w-full border border-gray-300 rounded-lg p-2 text-sm h-20" placeholder="时间节点内容">这个怀表最初是由我的曾祖父在1920年购买的，当时他刚刚结婚，这是他作为一个成年男性的象征。</textarea>
                  </div>
                  <div class="flex items-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mr-2">
                      <i class="fas fa-image text-gray-400"></i>
                    </div>
                    <button class="text-xs text-blue-500">
                      <i class="fas fa-plus mr-1"></i>
                      添加图片
                    </button>
                  </div>
                </div>

                <div class="timeline-item">
                  <div class="timeline-dot"></div>
                  <div class="mb-2">
                    <input type="text" class="w-full border border-gray-300 rounded-lg p-2 text-sm" placeholder="时间节点标题" value="1945年 - 战争中的守护" />
                  </div>
                  <div class="mb-2">
                    <textarea class="w-full border border-gray-300 rounded-lg p-2 text-sm h-20" placeholder="时间节点内容">在二战期间，我的祖父将这个怀表随身携带，它在一次战斗中挡住了一颗子弹，救了祖父一命。从那以后，这个怀表就被视为家族的幸运物。</textarea>
                  </div>
                  <div class="flex">
                    <div class="w-16 h-16 rounded-lg overflow-hidden mr-2">
                      <img src="https://images.unsplash.com/photo-1577083552431-6e5fd01aa342" class="w-full h-full object-cover" alt="时间节点图片" />
                    </div>
                    <button class="text-xs text-blue-500">
                      <i class="fas fa-plus mr-1"></i>
                      添加图片
                    </button>
                  </div>
                </div>

                <div class="timeline-item">
                  <div class="timeline-dot"></div>
                  <div class="mb-2">
                    <input type="text" class="w-full border border-gray-300 rounded-lg p-2 text-sm" placeholder="时间节点标题" value="1980年 - 传给爷爷" />
                  </div>
                  <div class="mb-2">
                    <textarea class="w-full border border-gray-300 rounded-lg p-2 text-sm h-20" placeholder="时间节点内容">1980年，在我祖父去世前，他将怀表传给了我的爷爷，嘱咐他要好好保管这个家族的传家宝。</textarea>
                  </div>
                  <div class="flex items-center">
                    <div class="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center mr-2">
                      <i class="fas fa-image text-gray-400"></i>
                    </div>
                    <button class="text-xs text-blue-500">
                      <i class="fas fa-plus mr-1"></i>
                      添加图片
                    </button>
                  </div>
                </div>

                <button class="w-full border border-dashed border-gray-300 rounded-lg p-3 text-gray-500 mt-2">
                  <i class="fas fa-plus mr-1"></i>
                  添加新的时间节点
                </button>
              </div>
            </div>
          </div>

          <!-- 标签部分 -->
          <div class="mb-4">
            <label class="font-medium block mb-2">标签</label>
            <div class="tag-container">
              <div class="tag">
                家族传承
                <span class="tag-remove"><i class="fas fa-times"></i></span>
              </div>
              <div class="tag">
                历史记忆
                <span class="tag-remove"><i class="fas fa-times"></i></span>
              </div>
              <div class="tag">
                怀表
                <span class="tag-remove"><i class="fas fa-times"></i></span>
              </div>
              <div class="add-tag-btn">
                <i class="fas fa-plus mr-1"></i>
                添加标签
              </div>
            </div>
          </div>

          <!-- AI助手 - 优化版 -->
          <div class="mb-4">
            <div class="font-medium mb-3">AI助手</div>

            <div class="ai-assistant-card mb-4">
              <div class="flex items-start">
                <div class="ai-avatar mr-3">
                  <i class="fas fa-robot"></i>
                </div>
                <div class="flex-1">
                  <div class="font-medium mb-1">AI创作助手</div>
                  <p class="text-sm text-gray-600 mb-3">我可以帮助您完善故事细节，生成内容建议，或创建视觉效果</p>

                  <div class="ai-suggestion">
                    <p class="text-sm mb-2">检测到您的故事与"家族传承"和"历史记忆"相关，是否需要：</p>
                    <div class="flex flex-wrap gap-2 mt-2">
                      <button class="ai-action-btn text-xs">
                        <i class="fas fa-magic"></i>
                        丰富历史背景
                      </button>
                      <button class="ai-action-btn text-xs">
                        <i class="fas fa-pen"></i>
                        完善人物描写
                      </button>
                    </div>
                  </div>

                  <div class="ai-suggestion">
                    <p class="text-sm mb-2">检测到您添加了怀表照片，可以：</p>
                    <div class="flex flex-wrap gap-2 mt-2">
                      <button class="ai-action-btn text-xs">
                        <i class="fas fa-cube"></i>
                        生成3D模型
                      </button>
                      <button class="ai-action-btn text-xs">
                        <i class="fas fa-image"></i>
                        修复老照片
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-2 gap-3">
              <button class="ai-action-btn">
                <i class="fas fa-magic"></i>
                AI润色内容
              </button>
              <button class="ai-action-btn">
                <i class="fas fa-cube"></i>
                3D建模
              </button>
              <button class="ai-action-btn">
                <i class="fas fa-image"></i>
                图片增强
              </button>
              <button class="ai-action-btn">
                <i class="fas fa-microphone"></i>
                语音转文字
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 底部导航栏 -->
      <div class="nav-bar">
        <div class="nav-item">
          <i class="fas fa-home nav-icon"></i>
          <span>首页</span>
        </div>
        <div class="nav-item">
          <i class="fas fa-book nav-icon"></i>
          <span>故事集</span>
        </div>
        <div class="nav-item">
          <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
            <i class="fas fa-plus"></i>
          </div>
        </div>
        <div class="nav-item">
          <i class="fas fa-robot nav-icon"></i>
          <span>AI</span>
        </div>
        <div class="nav-item active">
          <i class="fas fa-user nav-icon"></i>
          <span>我的</span>
        </div>
      </div>
    </div>

    <!-- 全屏预览图片 -->
    <div class="fullscreen-overlay" id="fullscreen-overlay">
      <div class="fullscreen-close" onclick="closeFullscreen()">
        <i class="fas fa-times"></i>
      </div>
      <img src="" alt="全屏预览" class="fullscreen-image" id="fullscreen-image" />
    </div>

    <script src="https://cdn.jsdelivr.net/npm/swiper@8/swiper-bundle.min.js"></script>
    <script>
      // 初始化Swiper
      const swiper = new Swiper(".mySwiper", {
        pagination: {
          el: ".swiper-pagination",
          clickable: true,
        },
      });

      // 模式切换
      function switchMode(mode) {
        document.querySelectorAll(".mode-option").forEach(opt => {
          opt.classList.remove("active");
        });
        document.querySelectorAll(".mode-content").forEach(content => {
          content.classList.add("hidden");
        });

        document.querySelector(`.mode-option:nth-child(${mode === "normal" ? 1 : 2})`).classList.add("active");
        document.getElementById(`${mode}-mode`).classList.remove("hidden");
      }

      // 全屏预览功能
      function openFullscreen(element) {
        const imgSrc = element.querySelector("img").src;
        document.getElementById("fullscreen-image").src = imgSrc;
        document.getElementById("fullscreen-overlay").style.display = "flex";
      }

      function closeFullscreen() {
        document.getElementById("fullscreen-overlay").style.display = "none";
      }

      // 标签删除功能
      document.querySelectorAll(".tag-remove").forEach(btn => {
        btn.addEventListener("click", function () {
          this.parentElement.remove();
        });
      });

      // 添加标签功能
      document.querySelector(".add-tag-btn").addEventListener("click", function () {
        const tagName = prompt("请输入新标签名称:");
        if (tagName && tagName.trim() !== "") {
          const tagContainer = document.querySelector(".tag-container");
          const newTag = document.createElement("div");
          newTag.className = "tag";
          newTag.innerHTML = `
            ${tagName} 
            <span class="tag-remove"><i class="fas fa-times"></i></span>
          `;
          tagContainer.insertBefore(newTag, this);

          // 添加删除事件
          newTag.querySelector(".tag-remove").addEventListener("click", function () {
            this.parentElement.remove();
          });
        }
      });
    </script>
  </body>
</html>
