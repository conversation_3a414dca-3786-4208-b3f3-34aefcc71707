<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>记忆博物馆 - AI美化</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
    <style>
        :root {
            --primary: #4F46E5;
            --primary-light: #818CF8;
            --secondary: #F97316;
            --secondary-light: #FB923C;
            --gradient-start: #4F46E5;
            --gradient-end: #7C3AED;
        }
        
        * {
            font-family: 'Noto Sans SC', sans-serif;
            transition: all 0.3s ease;
        }
        
        body {
            background: #f5f7fa;
        }
        
        .phone-frame {
            width: 390px;
            height: 844px;
            border-radius: 40px;
            overflow: hidden;
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            margin: 0 auto;
            position: relative;
            background: white;
        }
        
        .status-bar {
            height: 44px;
            background: white;
            display: flex;
            justify-content: space-between;
            padding: 0 1.5rem;
            align-items: center;
            font-weight: 600;
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            z-index: 20;
        }
        
        .screen-content {
            height: 100%;
            overflow-y: auto;
            padding-top: 44px;
            padding-bottom: 80px;
            scroll-behavior: smooth;
        }
        
        .screen-content::-webkit-scrollbar {
            display: none;
        }
        
        .nav-bar {
            height: 80px;
            background: white;
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            justify-content: space-around;
            align-items: center;
            border-top: 1px solid #eee;
            box-shadow: 0 -5px 10px rgba(0, 0, 0, 0.03);
        }
        
        .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            font-size: 0.75rem;
            color: #94a3b8;
            cursor: pointer;
        }
        
        .nav-icon {
            font-size: 1.5rem;
            margin-bottom: 0.25rem;
        }
        
        .nav-item.active {
            color: var(--primary);
            font-weight: 500;
        }
        
        .tab-button {
            padding: 0.75rem 1.5rem;
            border-radius: 9999px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .tab-button.active {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
        }
        
        .story-card {
            border-radius: 16px;
            overflow: hidden;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            transform: translateY(0);
        }
        
        .story-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .beautify-btn {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            color: white;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(79, 70, 229, 0.3);
            transition: all 0.3s ease;
        }
        
        .beautify-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 15px rgba(79, 70, 229, 0.4);
        }
        
        .view-btn {
            background: #f1f5f9;
            color: #64748b;
            padding: 0.5rem 1rem;
            border-radius: 9999px;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .view-btn:hover {
            background: #e2e8f0;
        }
        
        .feature-card {
            border-radius: 16px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
        }
        
        .feature-icon {
            width: 48px;
            height: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 12px;
            font-size: 1.25rem;
        }
        
        .add-btn {
            background: linear-gradient(135deg, var(--gradient-start), var(--gradient-end));
            width: 56px;
            height: 56px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            box-shadow: 0 8px 20px rgba(79, 70, 229, 0.4);
            transform: translateY(-10px);
            transition: all 0.3s ease;
        }
        
        .add-btn:hover {
            transform: translateY(-15px) scale(1.05);
            box-shadow: 0 12px 25px rgba(79, 70, 229, 0.5);
        }
        
        .badge {
            position: absolute;
            top: 10px;
            right: 10px;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
        }
        
        .badge-blue {
            background: linear-gradient(135deg, #4F46E5, #818CF8);
            color: white;
        }
        
        .badge-green {
            background: linear-gradient(135deg, #10B981, #34D399);
            color: white;
        }
        
        .header {
            background: white;
            position: sticky;
            top: 44px;
            z-index: 10;
            padding: 1rem 1rem 0.5rem;
            border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        }
        
        .section-title {
            font-size: 1.25rem;
            font-weight: 700;
            margin-bottom: 1rem;
            color: #1e293b;
        }
        
        .img-container {
            position: relative;
            overflow: hidden;
        }
        
        .img-container img {
            transition: transform 0.5s ease;
        }
        
        .story-card:hover .img-container img {
            transform: scale(1.05);
        }
        
        .img-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(to top, rgba(0, 0, 0, 0.3), transparent);
        }
    </style>
</head>
<body class="bg-gray-100">
    <div class="phone-frame">
        <div class="status-bar">
            <div>9:41</div>
            <div>
                <i class="fas fa-signal"></i>
                <i class="fas fa-wifi ml-1"></i>
                <i class="fas fa-battery-full ml-1"></i>
            </div>
        </div>
        <div class="screen-content">
            <div class="header">
                <div class="flex justify-between items-center mb-6">
                    <h1 class="text-2xl font-bold text-gray-800">AI美化</h1>
                    <div class="text-indigo-600 bg-indigo-50 p-2 rounded-full">
                        <i class="fas fa-cog"></i>
                    </div>
                </div>
                
                <!-- 分区选项卡 -->
                <div class="flex space-x-3 mb-4">
                    <button class="tab-button active">2D分区</button>
                    <button class="tab-button text-gray-600">3D分区</button>
                </div>
            </div>
            
            <div class="p-4">
                <!-- 故事列表 -->
                <div class="space-y-5">
                    <div class="story-card bg-white">
                        <div class="relative img-container">
                            <img src="https://images.unsplash.com/photo-1507525428034-b723cf961d3e?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2073&q=80" alt="夏日海边之旅" class="w-full h-48 object-cover">
                            <div class="img-overlay"></div>
                            <div class="badge badge-blue">
                                可美化
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800">夏日海边之旅</h3>
                            <p class="text-gray-500 text-sm mt-1">2023年7月15日</p>
                            <div class="flex justify-between items-center mt-3">
                                <span class="text-xs text-gray-500 flex items-center">
                                    <i class="fas fa-image mr-1"></i> 共12张照片
                                </span>
                                <button class="beautify-btn text-sm">
                                    <i class="fas fa-magic mr-1"></i> 美化
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="story-card bg-white">
                        <div class="relative img-container">
                            <img src="https://images.unsplash.com/photo-1519501025264-65ba15a82390?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2064&q=80" alt="城市漫步" class="w-full h-48 object-cover">
                            <div class="img-overlay"></div>
                            <div class="badge badge-blue">
                                可美化
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800">城市漫步</h3>
                            <p class="text-gray-500 text-sm mt-1">2023年8月3日</p>
                            <div class="flex justify-between items-center mt-3">
                                <span class="text-xs text-gray-500 flex items-center">
                                    <i class="fas fa-image mr-1"></i> 共8张照片
                                </span>
                                <button class="beautify-btn text-sm">
                                    <i class="fas fa-magic mr-1"></i> 美化
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="story-card bg-white">
                        <div class="relative img-container">
                            <img src="https://images.unsplash.com/photo-1529156069898-49953e39b3ac?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=2072&q=80" alt="家庭聚会" class="w-full h-48 object-cover">
                            <div class="img-overlay"></div>
                            <div class="badge badge-green">
                                已美化
                            </div>
                        </div>
                        <div class="p-4">
                            <h3 class="font-bold text-gray-800">家庭聚会</h3>
                            <p class="text-gray-500 text-sm mt-1">2023年6月20日</p>
                            <div class="flex justify-between items-center mt-3">
                                <span class="text-xs text-gray-500 flex items-center">
                                    <i class="fas fa-image mr-1"></i> 共15张照片
                                </span>
                                <button class="view-btn text-sm">
                                    <i class="fas fa-eye mr-1"></i> 查看
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 其他功能入口 -->
                <div class="mt-8">
                    <h2 class="section-title">其他功能</h2>
                    <div class="grid grid-cols-2 gap-4">
                        <div class="feature-card bg-white p-4 flex items-center">
                            <div class="feature-icon bg-indigo-50 text-indigo-600 mr-3">
                                <i class="fas fa-magic"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-800">风格转换</h3>
                                <p class="text-xs text-gray-500 mt-1">多种艺术风格可选</p>
                            </div>
                        </div>
                        <div class="feature-card bg-white p-4 flex items-center">
                            <div class="feature-icon bg-purple-50 text-purple-600 mr-3">
                                <i class="fas fa-image"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-800">照片修复</h3>
                                <p class="text-xs text-gray-500 mt-1">修复老旧照片</p>
                            </div>
                        </div>
                        <div class="feature-card bg-white p-4 flex items-center mt-3">
                            <div class="feature-icon bg-orange-50 text-orange-600 mr-3">
                                <i class="fas fa-palette"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-800">色彩增强</h3>
                                <p class="text-xs text-gray-500 mt-1">让照片更加鲜艳</p>
                            </div>
                        </div>
                        <div class="feature-card bg-white p-4 flex items-center mt-3">
                            <div class="feature-icon bg-emerald-50 text-emerald-600 mr-3">
                                <i class="fas fa-wand-magic-sparkles"></i>
                            </div>
                            <div>
                                <h3 class="font-medium text-gray-800">智能修图</h3>
                                <p class="text-xs text-gray-500 mt-1">一键优化照片</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="nav-bar">
            <div class="nav-item">
                <i class="fas fa-home nav-icon"></i>
                <span>首页</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-book nav-icon"></i>
                <span>故事集</span>
            </div>
            <div class="nav-item">
                <div class="add-btn">
                    <i class="fas fa-plus"></i>
                </div>
            </div>
            <div class="nav-item active">
                <i class="fas fa-robot nav-icon"></i>
                <span>AI</span>
            </div>
            <div class="nav-item">
                <i class="fas fa-user nav-icon"></i>
                <span>我的</span>
            </div>
        </div>
    </div>
</body>
</html>