<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>添加物品</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      }
      .main-color {
        color: #ff7e5d;
      }
      .bg-main {
        background-color: #ff7e5d;
      }
      .photo-upload {
        border: 2px dashed #e5e5e5;
        border-radius: 12px;
        height: 100px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .form-input {
        border-radius: 12px;
        border: 1px solid #e5e5e5;
        padding: 12px 16px;
        width: 100%;
        font-size: 16px;
        outline: none;
      }
      .form-input:focus {
        border-color: #ff7e5d;
        box-shadow: 0 0 0 2px rgba(255, 126, 93, 0.2);
      }
      .form-label {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 6px;
        color: #374151;
      }
      .form-group {
        margin-bottom: 20px;
      }
      .rating-input {
        display: flex;
        gap: 8px;
      }
      .rating-star {
        font-size: 24px;
        color: #d1d5db;
        cursor: pointer;
      }
      .rating-star.active {
        color: #fbbf24;
      }
      .tag-item {
        background-color: #f3f4f6;
        border-radius: 16px;
        padding: 6px 12px;
        font-size: 14px;
        display: inline-flex;
        align-items: center;
        margin-right: 8px;
        margin-bottom: 8px;
      }
      .tag-item.active {
        background-color: #ff7e5d;
        color: white;
      }
    </style>
  </head>
  <body class="bg-white">
    <div class="p-4">
      <!-- 顶部导航 -->
      <div class="flex justify-between items-center mb-6">
        <button class="text-gray-800">
          <i class="fas fa-times text-xl"></i>
        </button>
        <h1 class="text-xl font-bold">添加物品</h1>
        <button class="text-main font-medium">保存</button>
      </div>

      <!-- 表单内容 -->
      <div class="mb-6">
        <div class="grid grid-cols-3 gap-3 mb-3">
          <div class="photo-upload">
            <i class="fas fa-camera text-gray-400 text-xl mb-1"></i>
            <span class="text-xs text-gray-500">添加照片</span>
          </div>
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1526170375885-4d8ecf77b99f" alt="相机照片" class="w-full h-24 object-cover rounded-lg" />
            <button class="absolute top-1 right-1 bg-black bg-opacity-50 text-white rounded-full w-6 h-6 flex items-center justify-center">
              <i class="fas fa-times text-xs"></i>
            </button>
          </div>
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1452780212940-6f5c0d14d848" alt="相机照片" class="w-full h-24 object-cover rounded-lg" />
            <button class="absolute top-1 right-1 bg-black bg-opacity-50 text-white rounded-full w-6 h-6 flex items-center justify-center">
              <i class="fas fa-times text-xs"></i>
            </button>
          </div>
        </div>
        <p class="text-xs text-gray-500">上传至少3张照片可生成3D模型</p>
      </div>

      <div class="form-group">
        <label class="form-label">物品名称</label>
        <input type="text" class="form-input" placeholder="输入物品名称" value="复古相机" />
      </div>

      <div class="form-group">
        <label class="form-label">分类标签</label>
        <div class="mt-2">
          <span class="tag-item active">收藏品</span>
          <span class="tag-item">电子产品</span>
          <span class="tag-item">摄影器材</span>
          <span class="tag-item">复古物品</span>
          <span class="tag-item">
            <i class="fas fa-plus mr-1 text-xs"></i>
            添加标签
          </span>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <div class="form-group">
          <label class="form-label">品牌</label>
          <input type="text" class="form-input" placeholder="输入品牌" value="Canon" />
        </div>
        <div class="form-group">
          <label class="form-label">购买日期</label>
          <div class="relative">
            <input type="text" class="form-input" placeholder="选择日期" value="2020-05-10" />
            <i class="fas fa-calendar absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
          </div>
        </div>
      </div>

      <div class="grid grid-cols-2 gap-4">
        <div class="form-group">
          <label class="form-label">购买价格</label>
          <div class="relative">
            <input type="text" class="form-input" placeholder="输入价格" value="2800" />
            <span class="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-500">¥</span>
            <input type="text" class="form-input pl-8" placeholder="输入价格" value="2800" />
          </div>
        </div>
        <div class="form-group">
          <label class="form-label">使用频率</label>
          <select class="form-input">
            <option>经常使用</option>
            <option selected>偶尔使用</option>
            <option>很少使用</option>
            <option>不再使用</option>
          </select>
        </div>
      </div>

      <div class="form-group">
        <label class="form-label">情感价值评分</label>
        <div class="rating-input">
          <i class="fas fa-star rating-star active"></i>
          <i class="fas fa-star rating-star active"></i>
          <i class="fas fa-star rating-star active"></i>
          <i class="fas fa-star rating-star active"></i>
          <i class="fas fa-star rating-star"></i>
        </div>
      </div>

      <div class="form-group">
        <div class="flex justify-between items-center mb-2">
          <label class="form-label mb-0">物品描述</label>
          <span class="text-xs text-gray-500">0/200</span>
        </div>
        <textarea class="form-input" rows="4" placeholder="描述这个物品...">这是一台2020年购入的Canon复古相机，保存完好，拍摄效果极佳，是我珍藏的摄影器材之一。</textarea>
      </div>

      <div class="mt-8">
        <button class="w-full bg-main text-white py-3 rounded-xl font-medium">保存并添加故事</button>
        <button class="w-full text-gray-700 py-3 mt-3 rounded-xl font-medium border border-gray-300">仅保存物品</button>
      </div>
    </div>
  </body>
</html>
