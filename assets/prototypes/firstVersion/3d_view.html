<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>3D展示</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        padding-bottom: 80px;
      }
      .main-color {
        color: #ff7e5d;
      }
      .bg-main {
        background-color: #ff7e5d;
      }
      .tab-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 80px;
        background-color: rgba(255, 255, 255, 0.95);
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #e5e5e5;
        padding-bottom: 20px;
      }
      .tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 10px;
      }
      .tab-icon {
        font-size: 24px;
        margin-bottom: 4px;
      }
      .active {
        color: #ff7e5d;
      }
      .model-container {
        height: 400px;
        background-color: #f9f9f9;
        position: relative;
        overflow: hidden;
        border-radius: 16px;
      }
      .model-placeholder {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
      }
      .control-button {
        width: 40px;
        height: 40px;
        border-radius: 20px;
        background-color: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      .touch-hint {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        animation: pulse 2s infinite;
      }
      @keyframes pulse {
        0% {
          opacity: 0.7;
          transform: translate(-50%, -50%) scale(1);
        }
        50% {
          opacity: 0.3;
          transform: translate(-50%, -50%) scale(1.2);
        }
        100% {
          opacity: 0.7;
          transform: translate(-50%, -50%) scale(1);
        }
      }
      .mode-button {
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 14px;
        font-weight: 500;
      }
      .mode-button.active {
        background-color: #ff7e5d;
        color: white;
      }
      .haptic-button {
        width: 60px;
        height: 60px;
        border-radius: 30px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        font-size: 10px;
        background-color: white;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div class="p-4">
      <!-- 顶部导航 -->
      <div class="flex justify-between items-center mb-6">
        <button class="text-gray-800">
          <i class="fas fa-arrow-left text-xl"></i>
        </button>
        <h1 class="text-xl font-bold">3D展示</h1>
        <button class="text-main font-medium">分享</button>
      </div>

      <!-- 物品信息 -->
      <div class="flex items-center mb-4">
        <img src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f" alt="复古相机" class="w-12 h-12 rounded-lg object-cover" />
        <div class="ml-3">
          <h2 class="font-medium">复古相机</h2>
          <div class="text-xs text-gray-500">收藏品</div>
        </div>
      </div>

      <!-- 3D模型展示区 -->
      <div class="model-container mb-4">
        <img src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f" alt="复古相机" class="w-full h-full object-contain opacity-70" />

        <!-- 3D模型控制器 -->
        <div class="absolute top-4 right-4 flex flex-col space-y-3">
          <button class="control-button">
            <i class="fas fa-expand-arrows-alt text-gray-700"></i>
          </button>
          <button class="control-button">
            <i class="fas fa-redo text-gray-700"></i>
          </button>
          <button class="control-button">
            <i class="fas fa-search-plus text-gray-700"></i>
          </button>
        </div>

        <!-- 触摸提示 -->
        <div class="touch-hint">
          <div class="bg-black bg-opacity-70 text-white px-3 py-2 rounded-full text-xs">
            <i class="fas fa-hand-pointer mr-1"></i>
            单指旋转 · 双指缩放
          </div>
        </div>

        <!-- 3D模型加载中 -->
        <div class="model-placeholder text-center">
          <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-main mx-auto mb-2"></div>
          <div class="text-gray-500 text-sm">3D模型生成中...</div>
          <div class="text-xs text-gray-400 mt-1">预计剩余时间：5秒</div>
        </div>
      </div>

      <!-- 展示模式切换 -->
      <div class="flex justify-center space-x-3 mb-6">
        <button class="mode-button">2D展示</button>
        <button class="mode-button active">3D模型</button>
        <button class="mode-button">AR融合</button>
      </div>

      <!-- 触感模拟控制 -->
      <div class="bg-white p-4 rounded-xl mb-6">
        <h3 class="font-medium mb-3">触感模拟</h3>
        <div class="flex justify-between items-center mb-4">
          <div>
            <div class="text-sm font-medium">材质识别</div>
            <div class="text-xs text-gray-500">金属 + 塑料混合材质</div>
          </div>
          <div class="bg-gray-100 text-xs px-3 py-1 rounded-full">AI识别</div>
        </div>

        <div class="flex justify-between mb-4">
          <div class="haptic-button">
            <i class="fas fa-tint mb-1"></i>
            <span>光滑</span>
          </div>
          <div class="haptic-button bg-main text-white">
            <i class="fas fa-grip-lines mb-1"></i>
            <span>纹理</span>
          </div>
          <div class="haptic-button">
            <i class="fas fa-dice-d6 mb-1"></i>
            <span>颗粒</span>
          </div>
          <div class="haptic-button">
            <i class="fas fa-plus mb-1"></i>
            <span>自定义</span>
          </div>
        </div>

        <div class="mb-2">
          <div class="flex justify-between items-center mb-1">
            <div class="text-sm">震动强度</div>
            <div class="text-xs text-gray-500">75%</div>
          </div>
          <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div class="h-full bg-main w-3/4"></div>
          </div>
        </div>

        <div>
          <div class="flex justify-between items-center mb-1">
            <div class="text-sm">响应灵敏度</div>
            <div class="text-xs text-gray-500">50%</div>
          </div>
          <div class="h-2 bg-gray-200 rounded-full overflow-hidden">
            <div class="h-full bg-main w-1/2"></div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-3">
        <button class="flex-1 border border-main text-main py-3 rounded-xl font-medium">
          <i class="fas fa-share-alt mr-1"></i>
          分享模型
        </button>
        <button class="flex-1 bg-main text-white py-3 rounded-xl font-medium">
          <i class="fas fa-vr-cardboard mr-1"></i>
          AR展示
        </button>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
      <div class="tab-item">
        <i class="fas fa-home tab-icon"></i>
        <span>首页</span>
      </div>
      <div class="tab-item active">
        <i class="fas fa-cube tab-icon"></i>
        <span>展示</span>
      </div>
      <div class="tab-item">
        <div class="bg-main rounded-full w-14 h-14 flex items-center justify-center -mt-5">
          <i class="fas fa-plus text-white text-xl"></i>
        </div>
      </div>
      <div class="tab-item">
        <i class="fas fa-users tab-icon"></i>
        <span>社区</span>
      </div>
      <div class="tab-item">
        <i class="fas fa-user tab-icon"></i>
        <span>我的</span>
      </div>
    </div>
  </body>
</html>
