<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>个人资料</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        padding-bottom: 80px;
      }
      .main-color {
        color: #ff7e5d;
      }
      .bg-main {
        background-color: #ff7e5d;
      }
      .tab-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 80px;
        background-color: rgba(255, 255, 255, 0.95);
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #e5e5e5;
        padding-bottom: 20px;
      }
      .tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 10px;
      }
      .tab-icon {
        font-size: 24px;
        margin-bottom: 4px;
      }
      .active {
        color: #ff7e5d;
      }
      .profile-header {
        background-color: #ff7e5d;
        height: 120px;
        border-radius: 0 0 30px 30px;
        position: relative;
      }
      .profile-avatar {
        width: 80px;
        height: 80px;
        border-radius: 40px;
        border: 4px solid white;
        position: absolute;
        bottom: -40px;
        left: 50%;
        transform: translateX(-50%);
        background-color: white;
      }
      .stat-item {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .menu-item {
        display: flex;
        align-items: center;
        padding: 16px;
        border-bottom: 1px solid #f3f4f6;
      }
      .menu-icon {
        width: 40px;
        height: 40px;
        border-radius: 20px;
        background-color: #f3f4f6;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;
      }
      .badge {
        background-color: #ff7e5d;
        color: white;
        border-radius: 10px;
        padding: 2px 8px;
        font-size: 10px;
      }
      .item-grid {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 8px;
      }
      .item-thumbnail {
        aspect-ratio: 1;
        border-radius: 8px;
        overflow: hidden;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <!-- 个人资料头部 -->
    <div class="profile-header">
      <div class="absolute top-0 left-0 right-0 p-4 flex justify-between items-center">
        <h1 class="text-xl font-bold text-white">个人资料</h1>
        <button class="text-white">
          <i class="fas fa-cog text-xl"></i>
        </button>
      </div>
      <div class="profile-avatar">
        <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="用户头像" class="w-full h-full rounded-full object-cover" />
      </div>
    </div>

    <!-- 个人信息 -->
    <div class="mt-12 text-center mb-6">
      <h2 class="text-xl font-bold">林小雨</h2>
      <p class="text-gray-500 text-sm mt-1">物品收藏爱好者 · 上海</p>
      <div class="flex justify-center mt-2">
        <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full mr-2">收藏家</span>
        <span class="bg-gray-100 text-gray-700 text-xs px-2 py-1 rounded-full">摄影爱好者</span>
      </div>
    </div>

    <!-- 统计数据 -->
    <div class="flex justify-around mb-6 px-4">
      <div class="stat-item">
        <div class="text-xl font-bold">42</div>
        <div class="text-xs text-gray-500">物品</div>
      </div>
      <div class="stat-item">
        <div class="text-xl font-bold">128</div>
        <div class="text-xs text-gray-500">故事</div>
      </div>
      <div class="stat-item">
        <div class="text-xl font-bold">15</div>
        <div class="text-xs text-gray-500">交换</div>
      </div>
      <div class="stat-item">
        <div class="text-xl font-bold">256</div>
        <div class="text-xs text-gray-500">关注者</div>
      </div>
    </div>

    <!-- 功能菜单 -->
    <div class="bg-white rounded-xl mb-6 mx-4">
      <div class="menu-item">
        <div class="menu-icon">
          <i class="fas fa-cube text-gray-600"></i>
        </div>
        <div class="flex-1">
          <div class="font-medium">我的物品</div>
          <div class="text-xs text-gray-500">管理您的所有物品</div>
        </div>
        <i class="fas fa-chevron-right text-gray-400"></i>
      </div>

      <div class="menu-item">
        <div class="menu-icon">
          <i class="fas fa-exchange-alt text-gray-600"></i>
        </div>
        <div class="flex-1">
          <div class="font-medium">交换记录</div>
          <div class="text-xs text-gray-500">查看历史交换和进行中的交换</div>
        </div>
        <div class="flex items-center">
          <span class="badge mr-2">2</span>
          <i class="fas fa-chevron-right text-gray-400"></i>
        </div>
      </div>

      <div class="menu-item">
        <div class="menu-icon">
          <i class="fas fa-heart text-gray-600"></i>
        </div>
        <div class="flex-1">
          <div class="font-medium">收藏夹</div>
          <div class="text-xs text-gray-500">您收藏的物品和故事</div>
        </div>
        <i class="fas fa-chevron-right text-gray-400"></i>
      </div>

      <div class="menu-item">
        <div class="menu-icon">
          <i class="fas fa-leaf text-gray-600"></i>
        </div>
        <div class="flex-1">
          <div class="font-medium">环保积分</div>
          <div class="text-xs text-gray-500">您的环保贡献和积分</div>
        </div>
        <div class="flex items-center">
          <span class="text-main font-medium mr-2">320分</span>
          <i class="fas fa-chevron-right text-gray-400"></i>
        </div>
      </div>
    </div>

    <!-- 我的物品展示 -->
    <div class="mx-4 mb-6">
      <div class="flex justify-between items-center mb-3">
        <h3 class="font-semibold">我的物品</h3>
        <button class="text-main text-sm">查看全部</button>
      </div>

      <div class="item-grid">
        <div class="item-thumbnail">
          <img src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f" alt="复古相机" class="w-full h-full object-cover" />
        </div>
        <div class="item-thumbnail">
          <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff" alt="手表" class="w-full h-full object-cover" />
        </div>
        <div class="item-thumbnail">
          <img src="https://images.unsplash.com/photo-1511499767150-a48a237f0083" alt="耳机" class="w-full h-full object-cover" />
        </div>
        <div class="item-thumbnail">
          <img src="https://images.unsplash.com/photo-1560243563-062bfc001d68" alt="运动鞋" class="w-full h-full object-cover" />
        </div>
        <div class="item-thumbnail">
          <img src="https://images.unsplash.com/photo-1600185365483-26d7a4cc7519" alt="机械键盘" class="w-full h-full object-cover" />
        </div>
        <div class="item-thumbnail">
          <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105" alt="相册" class="w-full h-full object-cover" />
        </div>
      </div>
    </div>

    <!-- 订阅信息 -->
    <div class="bg-white rounded-xl p-4 mx-4 mb-6">
      <div class="flex justify-between items-center mb-3">
        <div>
          <h3 class="font-semibold">高级会员</h3>
          <p class="text-xs text-gray-500">解锁全部高级功能</p>
        </div>
        <span class="bg-main text-white text-xs px-3 py-1 rounded-full">¥15/月</span>
      </div>

      <div class="flex items-center text-sm mb-2">
        <i class="fas fa-check text-main mr-2"></i>
        <span>无限3D模型生成</span>
      </div>
      <div class="flex items-center text-sm mb-2">
        <i class="fas fa-check text-main mr-2"></i>
        <span>高级触感库</span>
      </div>
      <div class="flex items-center text-sm">
        <i class="fas fa-check text-main mr-2"></i>
        <span>物品估值报告</span>
      </div>

      <button class="w-full bg-main text-white py-2 rounded-lg mt-3 text-sm font-medium">立即升级</button>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
      <div class="tab-item">
        <i class="fas fa-home tab-icon"></i>
        <span>首页</span>
      </div>
      <div class="tab-item">
        <i class="fas fa-cube tab-icon"></i>
        <span>展示</span>
      </div>
      <div class="tab-item">
        <div class="bg-main rounded-full w-14 h-14 flex items-center justify-center -mt-5">
          <i class="fas fa-plus text-white text-xl"></i>
        </div>
      </div>
      <div class="tab-item">
        <i class="fas fa-users tab-icon"></i>
        <span>社区</span>
      </div>
      <div class="tab-item active">
        <i class="fas fa-user tab-icon"></i>
        <span>我的</span>
      </div>
    </div>
  </body>
</html>
