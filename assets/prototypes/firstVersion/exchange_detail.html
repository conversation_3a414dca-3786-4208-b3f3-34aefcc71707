<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>交换详情</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      }
      .main-color {
        color: #ff7e5d;
      }
      .bg-main {
        background-color: #ff7e5d;
      }
      .item-card {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
      .value-badge {
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 12px;
        padding: 2px 8px;
        color: white;
        font-size: 10px;
        position: absolute;
        bottom: 10px;
        left: 10px;
        display: flex;
        align-items: center;
      }
      .exchange-arrow {
        width: 40px;
        height: 40px;
        border-radius: 20px;
        background-color: #ff7e5d;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        box-shadow: 0 2px 8px rgba(255, 126, 93, 0.3);
      }
      .safety-item {
        display: flex;
        align-items: center;
        padding: 12px;
        border-radius: 12px;
        background-color: #f9f9f9;
        margin-bottom: 10px;
      }
      .safety-icon {
        width: 36px;
        height: 36px;
        border-radius: 18px;
        background-color: rgba(255, 126, 93, 0.1);
        display: flex;
        align-items: center;
        justify-center: center;
        color: #ff7e5d;
        margin-right: 12px;
      }
      .action-button {
        border-radius: 12px;
        padding: 14px 0;
        font-weight: 500;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div class="p-4">
      <!-- 顶部导航 -->
      <div class="flex justify-between items-center mb-6">
        <button class="text-gray-800">
          <i class="fas fa-arrow-left text-xl"></i>
        </button>
        <h1 class="text-xl font-bold">交换详情</h1>
        <button class="text-main font-medium">
          <i class="fas fa-ellipsis-h"></i>
        </button>
      </div>

      <!-- 交换物品展示 -->
      <div class="flex items-center justify-between mb-6">
        <!-- 我的物品 -->
        <div class="item-card bg-white w-2/5">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f" alt="复古相机" class="w-full h-32 object-cover" />
            <div class="value-badge">
              <i class="fas fa-tag mr-1"></i>
              ¥2,800
            </div>
          </div>
          <div class="p-3">
            <h3 class="font-medium">复古相机</h3>
            <div class="text-xs text-gray-500">我的物品</div>
          </div>
        </div>

        <!-- 交换箭头 -->
        <div class="exchange-arrow">
          <i class="fas fa-exchange-alt"></i>
        </div>

        <!-- 对方物品 -->
        <div class="item-card bg-white w-2/5">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff" alt="手表" class="w-full h-32 object-cover" />
            <div class="value-badge">
              <i class="fas fa-tag mr-1"></i>
              ¥5,600
            </div>
          </div>
          <div class="p-3">
            <h3 class="font-medium">限量版机械表</h3>
            <div class="text-xs text-gray-500">对方物品</div>
          </div>
        </div>
      </div>

      <!-- 交换信息 -->
      <div class="bg-white p-4 rounded-xl mb-6">
        <div class="flex items-center mb-4">
          <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像" class="w-12 h-12 rounded-full object-cover" />
          <div class="ml-3">
            <div class="font-medium">陈大明</div>
            <div class="flex items-center text-xs text-gray-500">
              <i class="fas fa-star text-yellow-400 mr-1"></i>
              4.9 · 32次成功交换
            </div>
          </div>
          <button class="ml-auto bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">
            <i class="fas fa-comment-alt mr-1"></i>
            联系
          </button>
        </div>

        <div class="border-t border-gray-100 pt-4">
          <div class="flex justify-between mb-3">
            <div class="text-sm text-gray-500">交换方式</div>
            <div class="font-medium">等价置换</div>
          </div>
          <div class="flex justify-between mb-3">
            <div class="text-sm text-gray-500">交换地点</div>
            <div class="font-medium">上海市徐汇区某咖啡厅</div>
          </div>
          <div class="flex justify-between mb-3">
            <div class="text-sm text-gray-500">交换时间</div>
            <div class="font-medium">2025年7月5日 14:00</div>
          </div>
          <div class="flex justify-between">
            <div class="text-sm text-gray-500">交换状态</div>
            <div class="text-main font-medium">等待确认</div>
          </div>
        </div>
      </div>

      <!-- 价值评估 -->
      <div class="bg-white p-4 rounded-xl mb-6">
        <h3 class="font-medium mb-4">价值评估</h3>

        <div class="flex justify-between mb-4">
          <div>
            <div class="text-sm font-medium">市场价值</div>
            <div class="flex items-center">
              <div class="text-lg font-bold">¥2,800</div>
              <div class="text-xs text-gray-500 ml-2">我的物品</div>
            </div>
          </div>
          <div class="text-right">
            <div class="text-sm font-medium">市场价值</div>
            <div class="flex items-center justify-end">
              <div class="text-lg font-bold">¥5,600</div>
              <div class="text-xs text-gray-500 ml-2">对方物品</div>
            </div>
          </div>
        </div>

        <div class="h-2 bg-gray-200 rounded-full overflow-hidden mb-4">
          <div class="h-full bg-main w-1/3"></div>
        </div>

        <div class="flex justify-between">
          <div>
            <div class="text-sm font-medium">情感价值</div>
            <div class="flex">
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-gray-300"></i>
            </div>
          </div>
          <div class="text-right">
            <div class="text-sm font-medium">情感价值</div>
            <div class="flex justify-end">
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-yellow-400"></i>
            </div>
          </div>
        </div>

        <div class="mt-4 p-3 bg-yellow-50 rounded-lg border border-yellow-100">
          <div class="flex items-start">
            <i class="fas fa-exclamation-circle text-yellow-500 mt-0.5 mr-2"></i>
            <div class="text-sm text-yellow-700">对方物品市场价值高于您的物品，建议补充差价或提供额外物品进行等价交换。</div>
          </div>
        </div>
      </div>

      <!-- 交换安全 -->
      <div class="bg-white p-4 rounded-xl mb-6">
        <h3 class="font-medium mb-3">交换安全保障</h3>

        <div class="safety-item">
          <div class="safety-icon flex items-center justify-center">
            <i class="fas fa-video"></i>
          </div>
          <div>
            <div class="font-medium text-sm">视频验证</div>
            <div class="text-xs text-gray-500">交换过程中开启实时视频验证物品真实性</div>
          </div>
        </div>

        <div class="safety-item">
          <div class="safety-icon flex items-center justify-center">
            <i class="fas fa-map-marker-alt"></i>
          </div>
          <div>
            <div class="font-medium text-sm">位置共享</div>
            <div class="text-xs text-gray-500">交换时自动开启位置共享，确保安全</div>
          </div>
        </div>

        <div class="safety-item">
          <div class="safety-icon flex items-center justify-center">
            <i class="fas fa-file-signature"></i>
          </div>
          <div>
            <div class="font-medium text-sm">电子协议</div>
            <div class="text-xs text-gray-500">双方签署电子交换协议，明确权责</div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="flex space-x-3">
        <button class="flex-1 border border-gray-300 text-gray-700 action-button">拒绝交换</button>
        <button class="flex-1 bg-main text-white action-button">确认交换</button>
      </div>
    </div>
  </body>
</html>
