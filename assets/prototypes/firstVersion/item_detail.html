<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>物品详情</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        padding-bottom: 80px;
      }
      .main-color {
        color: #ff7e5d;
      }
      .bg-main {
        background-color: #ff7e5d;
      }
      .tab-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 80px;
        background-color: rgba(255, 255, 255, 0.95);
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #e5e5e5;
        padding-bottom: 20px;
      }
      .tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 10px;
      }
      .tab-icon {
        font-size: 24px;
        margin-bottom: 4px;
      }
      .active {
        color: #ff7e5d;
      }
      .image-slider {
        position: relative;
        height: 300px;
      }
      .image-indicator {
        position: absolute;
        bottom: 16px;
        left: 0;
        right: 0;
        display: flex;
        justify-content: center;
        gap: 6px;
      }
      .indicator-dot {
        width: 8px;
        height: 8px;
        border-radius: 4px;
        background-color: rgba(255, 255, 255, 0.5);
      }
      .indicator-dot.active {
        width: 20px;
        background-color: white;
      }
      .info-card {
        border-radius: 20px 20px 0 0;
        margin-top: -20px;
        position: relative;
        z-index: 10;
      }
      .timeline-item {
        position: relative;
        padding-left: 30px;
      }
      .timeline-item::before {
        content: "";
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #e5e5e5;
      }
      .timeline-dot {
        position: absolute;
        left: 6px;
        top: 6px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #ff7e5d;
        z-index: 1;
      }
      .action-button {
        border-radius: 12px;
        padding: 12px 0;
        font-weight: 500;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div class="relative">
      <!-- 顶部导航 -->
      <div class="absolute top-0 left-0 right-0 z-10 p-4 flex justify-between">
        <button class="bg-black bg-opacity-30 text-white rounded-full w-10 h-10 flex items-center justify-center">
          <i class="fas fa-arrow-left"></i>
        </button>
        <div class="flex space-x-2">
          <button class="bg-black bg-opacity-30 text-white rounded-full w-10 h-10 flex items-center justify-center">
            <i class="fas fa-share-alt"></i>
          </button>
          <button class="bg-black bg-opacity-30 text-white rounded-full w-10 h-10 flex items-center justify-center">
            <i class="fas fa-ellipsis-h"></i>
          </button>
        </div>
      </div>

      <!-- 图片轮播 -->
      <div class="image-slider">
        <img src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f" alt="复古相机" class="w-full h-full object-cover" />
        <div class="image-indicator">
          <div class="indicator-dot active"></div>
          <div class="indicator-dot"></div>
          <div class="indicator-dot"></div>
          <div class="indicator-dot"></div>
        </div>
      </div>

      <!-- 物品信息卡片 -->
      <div class="info-card bg-white p-5">
        <div class="flex justify-between items-start mb-4">
          <div>
            <h1 class="text-2xl font-bold text-gray-900">复古相机</h1>
            <div class="text-sm text-gray-500 mt-1">收藏品</div>
          </div>
          <div class="flex flex-col items-end">
            <div class="flex">
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-yellow-400"></i>
              <i class="fas fa-star text-gray-300"></i>
            </div>
            <div class="text-sm text-gray-500 mt-1">情感价值评分</div>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4 mb-6">
          <div class="bg-gray-50 p-3 rounded-lg">
            <div class="text-sm text-gray-500">品牌</div>
            <div class="font-medium">Canon</div>
          </div>
          <div class="bg-gray-50 p-3 rounded-lg">
            <div class="text-sm text-gray-500">购买日期</div>
            <div class="font-medium">2020年5月10日</div>
          </div>
          <div class="bg-gray-50 p-3 rounded-lg">
            <div class="text-sm text-gray-500">购买价格</div>
            <div class="font-medium">¥2,800</div>
          </div>
          <div class="bg-gray-50 p-3 rounded-lg">
            <div class="text-sm text-gray-500">使用频率</div>
            <div class="font-medium">偶尔使用</div>
          </div>
        </div>

        <div class="mb-6">
          <div class="flex justify-between items-center mb-3">
            <h2 class="text-lg font-semibold">物品故事</h2>
            <button class="text-main text-sm font-medium">
              查看全部
              <i class="fas fa-chevron-right text-xs"></i>
            </button>
          </div>

          <div class="timeline-item pb-4">
            <div class="timeline-dot"></div>
            <div class="bg-gray-50 p-3 rounded-lg">
              <div class="flex justify-between items-center mb-2">
                <div class="font-medium">第一次使用</div>
                <div class="text-xs text-gray-500">2020年5月15日</div>
              </div>
              <p class="text-sm text-gray-700">这是我第一次使用这台相机，在公园拍摄了日落的照片，画质令人惊艳。</p>
              <div class="mt-2 flex space-x-2">
                <img src="https://images.unsplash.com/photo-1472120435266-53107fd0c44a" alt="日落照片" class="w-16 h-16 rounded-md object-cover" />
                <img src="https://images.unsplash.com/photo-1500382017468-9049fed747ef" alt="日落照片" class="w-16 h-16 rounded-md object-cover" />
              </div>
            </div>
          </div>

          <div class="timeline-item">
            <div class="timeline-dot"></div>
            <div class="bg-gray-50 p-3 rounded-lg">
              <div class="flex justify-between items-center mb-2">
                <div class="font-medium">旅行记忆</div>
                <div class="text-xs text-gray-500">2021年8月20日</div>
              </div>
              <p class="text-sm text-gray-700">带着相机去了云南，记录了整个旅程中的美好瞬间...</p>
            </div>
          </div>
        </div>

        <div class="mb-6">
          <h2 class="text-lg font-semibold mb-3">展示方式</h2>
          <div class="flex space-x-3">
            <button class="flex-1 bg-gray-100 rounded-xl p-3 flex flex-col items-center">
              <i class="fas fa-image text-xl mb-2 text-main"></i>
              <span class="text-sm">2D展示</span>
            </button>
            <button class="flex-1 bg-gray-100 rounded-xl p-3 flex flex-col items-center">
              <i class="fas fa-cube text-xl mb-2 text-main"></i>
              <span class="text-sm">3D模型</span>
            </button>
            <button class="flex-1 bg-gray-100 rounded-xl p-3 flex flex-col items-center">
              <i class="fas fa-vr-cardboard text-xl mb-2 text-main"></i>
              <span class="text-sm">AR展示</span>
            </button>
          </div>
        </div>

        <div class="flex space-x-3">
          <button class="flex-1 border border-main text-main action-button">编辑物品</button>
          <button class="flex-1 bg-main text-white action-button">分享至社区</button>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
      <div class="tab-item active">
        <i class="fas fa-home tab-icon"></i>
        <span>首页</span>
      </div>
      <div class="tab-item">
        <i class="fas fa-cube tab-icon"></i>
        <span>展示</span>
      </div>
      <div class="tab-item">
        <div class="bg-main rounded-full w-14 h-14 flex items-center justify-center -mt-5">
          <i class="fas fa-plus text-white text-xl"></i>
        </div>
      </div>
      <div class="tab-item">
        <i class="fas fa-users tab-icon"></i>
        <span>社区</span>
      </div>
      <div class="tab-item">
        <i class="fas fa-user tab-icon"></i>
        <span>我的</span>
      </div>
    </div>
  </body>
</html>
