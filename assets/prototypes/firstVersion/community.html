<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>社区</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        padding-bottom: 80px;
      }
      .main-color {
        color: #ff7e5d;
      }
      .bg-main {
        background-color: #ff7e5d;
      }
      .tab-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 80px;
        background-color: rgba(255, 255, 255, 0.95);
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #e5e5e5;
        padding-bottom: 20px;
      }
      .tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 10px;
      }
      .tab-icon {
        font-size: 24px;
        margin-bottom: 4px;
      }
      .active {
        color: #ff7e5d;
      }
      .post-card {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
      .story-badge {
        background-color: rgba(255, 126, 93, 0.9);
        border-radius: 12px;
        padding: 2px 8px;
        color: white;
        font-size: 10px;
        position: absolute;
        top: 10px;
        right: 10px;
      }
      .model-badge {
        background-color: rgba(79, 70, 229, 0.9);
        border-radius: 12px;
        padding: 2px 8px;
        color: white;
        font-size: 10px;
        position: absolute;
        top: 10px;
        right: 10px;
      }
      .category-pill {
        padding: 6px 16px;
        border-radius: 20px;
        font-size: 14px;
        white-space: nowrap;
      }
      .category-pill.active {
        background-color: #ff7e5d;
        color: white;
      }
      .value-badge {
        background-color: rgba(0, 0, 0, 0.7);
        border-radius: 12px;
        padding: 2px 8px;
        color: white;
        font-size: 10px;
        position: absolute;
        bottom: 10px;
        left: 10px;
        display: flex;
        align-items: center;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div class="p-4 pt-12">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">社区</h1>
        <div class="flex space-x-3">
          <button class="bg-gray-100 rounded-full w-10 h-10 flex items-center justify-center">
            <i class="fas fa-search text-gray-500"></i>
          </button>
          <button class="bg-gray-100 rounded-full w-10 h-10 flex items-center justify-center">
            <i class="fas fa-bell text-gray-500"></i>
          </button>
        </div>
      </div>

      <!-- 分类选项卡 -->
      <div class="overflow-x-auto mb-6">
        <div class="flex space-x-3 pb-2">
          <button class="category-pill active">推荐</button>
          <button class="category-pill bg-white">关注</button>
          <button class="category-pill bg-white">附近</button>
          <button class="category-pill bg-white">交换</button>
          <button class="category-pill bg-white">收藏品</button>
          <button class="category-pill bg-white">电子产品</button>
        </div>
      </div>

      <!-- 社区帖子 -->
      <div class="space-y-6">
        <!-- 帖子1 -->
        <div class="post-card bg-white">
          <div class="p-3 flex items-center">
            <img src="https://randomuser.me/api/portraits/women/44.jpg" alt="用户头像" class="w-10 h-10 rounded-full object-cover" />
            <div class="ml-3">
              <div class="font-medium">林小雨</div>
              <div class="text-xs text-gray-500">5分钟前</div>
            </div>
            <button class="ml-auto text-gray-400">
              <i class="fas fa-ellipsis-h"></i>
            </button>
          </div>

          <div class="relative">
            <img src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f" alt="复古相机" class="w-full h-64 object-cover" />
            <div class="story-badge">
              <i class="fas fa-book-open mr-1"></i>
              故事
            </div>
            <div class="value-badge">
              <i class="fas fa-tag mr-1"></i>
              估值：¥2,800
            </div>
          </div>

          <div class="p-4">
            <h3 class="font-medium text-lg mb-1">我珍藏的复古相机</h3>
            <p class="text-gray-600 text-sm mb-3">这台相机陪伴我走过了许多地方，记录了无数美好瞬间，每一张照片都承载着特别的回忆...</p>

            <div class="flex justify-between items-center">
              <div class="flex space-x-4">
                <button class="flex items-center text-gray-500">
                  <i class="far fa-heart mr-1"></i>
                  <span class="text-sm">128</span>
                </button>
                <button class="flex items-center text-gray-500">
                  <i class="far fa-comment mr-1"></i>
                  <span class="text-sm">32</span>
                </button>
              </div>
              <button class="flex items-center text-gray-500">
                <i class="fas fa-exchange-alt mr-1"></i>
                <span class="text-sm">交换</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 帖子2 -->
        <div class="post-card bg-white">
          <div class="p-3 flex items-center">
            <img src="https://randomuser.me/api/portraits/men/32.jpg" alt="用户头像" class="w-10 h-10 rounded-full object-cover" />
            <div class="ml-3">
              <div class="font-medium">陈大明</div>
              <div class="text-xs text-gray-500">2小时前</div>
            </div>
            <button class="ml-auto text-gray-400">
              <i class="fas fa-ellipsis-h"></i>
            </button>
          </div>

          <div class="relative">
            <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff" alt="手表" class="w-full h-64 object-cover" />
            <div class="model-badge">
              <i class="fas fa-cube mr-1"></i>
              3D模型
            </div>
            <div class="value-badge">
              <i class="fas fa-tag mr-1"></i>
              估值：¥5,600
            </div>
          </div>

          <div class="p-4">
            <h3 class="font-medium text-lg mb-1">限量版机械表</h3>
            <p class="text-gray-600 text-sm mb-3">这是我收藏的限量版机械表，全球限量500枚，寻找同款其他颜色交换，或者其他同等价值的收藏品...</p>

            <div class="flex justify-between items-center">
              <div class="flex space-x-4">
                <button class="flex items-center text-gray-500">
                  <i class="far fa-heart mr-1"></i>
                  <span class="text-sm">86</span>
                </button>
                <button class="flex items-center text-gray-500">
                  <i class="far fa-comment mr-1"></i>
                  <span class="text-sm">14</span>
                </button>
              </div>
              <button class="flex items-center text-main">
                <i class="fas fa-exchange-alt mr-1"></i>
                <span class="text-sm">交换</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 帖子3 -->
        <div class="post-card bg-white">
          <div class="p-3 flex items-center">
            <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="用户头像" class="w-10 h-10 rounded-full object-cover" />
            <div class="ml-3">
              <div class="font-medium">张小花</div>
              <div class="text-xs text-gray-500">昨天</div>
            </div>
            <button class="ml-auto text-gray-400">
              <i class="fas fa-ellipsis-h"></i>
            </button>
          </div>

          <div class="grid grid-cols-2 gap-1">
            <div class="relative">
              <img src="https://images.unsplash.com/photo-1511499767150-a48a237f0083" alt="耳机" class="w-full h-40 object-cover" />
            </div>
            <div class="relative">
              <img src="https://images.unsplash.com/photo-1505740420928-5e560c06d30e" alt="耳机" class="w-full h-40 object-cover" />
            </div>
            <div class="relative">
              <img src="https://images.unsplash.com/photo-1484704849700-f032a568e944" alt="耳机" class="w-full h-40 object-cover" />
            </div>
            <div class="relative">
              <img src="https://images.unsplash.com/photo-1524678606370-a47ad25cb82a" alt="耳机" class="w-full h-40 object-cover" />
            </div>
          </div>

          <div class="p-4">
            <h3 class="font-medium text-lg mb-1">我的耳机收藏</h3>
            <p class="text-gray-600 text-sm mb-3">分享我的耳机收藏，从入门到发烧，每一款都有不同的音色特点，最喜欢的还是那款复古造型的...</p>

            <div class="flex justify-between items-center">
              <div class="flex space-x-4">
                <button class="flex items-center text-gray-500">
                  <i class="far fa-heart mr-1"></i>
                  <span class="text-sm">215</span>
                </button>
                <button class="flex items-center text-gray-500">
                  <i class="far fa-comment mr-1"></i>
                  <span class="text-sm">47</span>
                </button>
              </div>
              <button class="flex items-center text-gray-500">
                <i class="fas fa-exchange-alt mr-1"></i>
                <span class="text-sm">交换</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
      <div class="tab-item">
        <i class="fas fa-home tab-icon"></i>
        <span>首页</span>
      </div>
      <div class="tab-item">
        <i class="fas fa-cube tab-icon"></i>
        <span>展示</span>
      </div>
      <div class="tab-item">
        <div class="bg-main rounded-full w-14 h-14 flex items-center justify-center -mt-5">
          <i class="fas fa-plus text-white text-xl"></i>
        </div>
      </div>
      <div class="tab-item active">
        <i class="fas fa-users tab-icon"></i>
        <span>社区</span>
      </div>
      <div class="tab-item">
        <i class="fas fa-user tab-icon"></i>
        <span>我的</span>
      </div>
    </div>
  </body>
</html>
