<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>故事编辑器</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
      }
      .main-color {
        color: #ff7e5d;
      }
      .bg-main {
        background-color: #ff7e5d;
      }
      .form-input {
        border-radius: 12px;
        border: 1px solid #e5e5e5;
        padding: 12px 16px;
        width: 100%;
        font-size: 16px;
        outline: none;
      }
      .form-input:focus {
        border-color: #ff7e5d;
        box-shadow: 0 0 0 2px rgba(255, 126, 93, 0.2);
      }
      .form-label {
        font-size: 14px;
        font-weight: 500;
        margin-bottom: 6px;
        color: #374151;
      }
      .form-group {
        margin-bottom: 20px;
      }
      .timeline-item {
        position: relative;
        padding-left: 30px;
        margin-bottom: 20px;
      }
      .timeline-item::before {
        content: "";
        position: absolute;
        left: 10px;
        top: 0;
        bottom: 0;
        width: 2px;
        background-color: #e5e5e5;
      }
      .timeline-dot {
        position: absolute;
        left: 6px;
        top: 6px;
        width: 10px;
        height: 10px;
        border-radius: 50%;
        background-color: #ff7e5d;
        z-index: 1;
      }
      .media-upload {
        border: 2px dashed #e5e5e5;
        border-radius: 12px;
        height: 80px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
      }
      .tool-button {
        width: 40px;
        height: 40px;
        border-radius: 20px;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: #f3f4f6;
      }
    </style>
  </head>
  <body class="bg-white">
    <div class="p-4">
      <!-- 顶部导航 -->
      <div class="flex justify-between items-center mb-6">
        <button class="text-gray-800">
          <i class="fas fa-arrow-left text-xl"></i>
        </button>
        <h1 class="text-xl font-bold">故事编辑器</h1>
        <button class="text-main font-medium">保存</button>
      </div>

      <!-- 物品信息 -->
      <div class="flex items-center mb-6 bg-gray-50 p-3 rounded-xl">
        <img src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f" alt="复古相机" class="w-16 h-16 rounded-lg object-cover" />
        <div class="ml-3">
          <h2 class="font-medium">复古相机</h2>
          <div class="text-sm text-gray-500">收藏品</div>
        </div>
      </div>

      <!-- 时间线编辑 -->
      <div class="mb-4">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold">故事时间线</h2>
          <button class="text-main text-sm font-medium">
            <i class="fas fa-plus mr-1"></i>
            添加节点
          </button>
        </div>

        <!-- 时间线节点1 -->
        <div class="timeline-item">
          <div class="timeline-dot"></div>
          <div class="bg-gray-50 p-4 rounded-lg">
            <div class="form-group">
              <label class="form-label">标题</label>
              <input type="text" class="form-input" placeholder="输入标题" value="第一次使用" />
            </div>

            <div class="form-group">
              <label class="form-label">日期</label>
              <div class="relative">
                <input type="text" class="form-input" placeholder="选择日期" value="2020-05-15" />
                <i class="fas fa-calendar absolute right-4 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
              </div>
            </div>

            <div class="form-group">
              <div class="flex justify-between items-center mb-2">
                <label class="form-label mb-0">故事内容</label>
                <span class="text-xs text-gray-500">32/500</span>
              </div>
              <textarea class="form-input" rows="4" placeholder="讲述你的故事...">这是我第一次使用这台相机，在公园拍摄了日落的照片，画质令人惊艳。</textarea>
            </div>

            <div class="form-group">
              <label class="form-label">添加媒体</label>
              <div class="grid grid-cols-4 gap-2 mb-2">
                <div class="relative">
                  <img src="https://images.unsplash.com/photo-1472120435266-53107fd0c44a" alt="日落照片" class="w-full h-16 object-cover rounded-lg" />
                  <button class="absolute top-1 right-1 bg-black bg-opacity-50 text-white rounded-full w-5 h-5 flex items-center justify-center">
                    <i class="fas fa-times text-xs"></i>
                  </button>
                </div>
                <div class="relative">
                  <img src="https://images.unsplash.com/photo-1500382017468-9049fed747ef" alt="日落照片" class="w-full h-16 object-cover rounded-lg" />
                  <button class="absolute top-1 right-1 bg-black bg-opacity-50 text-white rounded-full w-5 h-5 flex items-center justify-center">
                    <i class="fas fa-times text-xs"></i>
                  </button>
                </div>
                <div class="media-upload">
                  <i class="fas fa-plus text-gray-400 text-sm"></i>
                </div>
              </div>
              <div class="flex space-x-2">
                <button class="tool-button">
                  <i class="fas fa-image text-gray-500"></i>
                </button>
                <button class="tool-button">
                  <i class="fas fa-video text-gray-500"></i>
                </button>
                <button class="tool-button">
                  <i class="fas fa-microphone text-gray-500"></i>
                </button>
              </div>
            </div>

            <div class="flex justify-end">
              <button class="text-red-500 mr-3">
                <i class="fas fa-trash mr-1"></i>
                删除
              </button>
              <button class="text-gray-500">
                <i class="fas fa-chevron-down mr-1"></i>
                收起
              </button>
            </div>
          </div>
        </div>

        <!-- 时间线节点2 -->
        <div class="timeline-item">
          <div class="timeline-dot"></div>
          <div class="bg-gray-50 p-4 rounded-lg">
            <div class="flex justify-between items-center">
              <div>
                <div class="font-medium">旅行记忆</div>
                <div class="text-sm text-gray-500">2021年8月20日</div>
              </div>
              <button class="text-gray-500">
                <i class="fas fa-chevron-up"></i>
              </button>
            </div>
          </div>
        </div>

        <!-- 添加新节点 -->
        <button class="w-full border-2 border-dashed border-gray-200 rounded-lg p-3 text-gray-500 flex items-center justify-center">
          <i class="fas fa-plus mr-2"></i>
          添加新的故事节点
        </button>
      </div>

      <!-- AI辅助工具 -->
      <div class="bg-gray-50 p-4 rounded-xl mb-6">
        <div class="flex items-center mb-3">
          <div class="w-8 h-8 rounded-full bg-main flex items-center justify-center text-white mr-2">
            <i class="fas fa-robot"></i>
          </div>
          <h3 class="font-medium">AI故事助手</h3>
        </div>
        <p class="text-sm text-gray-600 mb-3">让AI帮你生成物品故事，或完善现有内容</p>
        <div class="flex space-x-2">
          <button class="flex-1 bg-white border border-gray-200 rounded-lg py-2 text-sm">生成故事</button>
          <button class="flex-1 bg-white border border-gray-200 rounded-lg py-2 text-sm">优化文字</button>
          <button class="flex-1 bg-white border border-gray-200 rounded-lg py-2 text-sm">提取关键点</button>
        </div>
      </div>

      <div class="mt-6">
        <button class="w-full bg-main text-white py-3 rounded-xl font-medium">保存故事</button>
      </div>
    </div>
  </body>
</html>
