<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>首页</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      body {
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
        padding-bottom: 80px;
      }
      .main-color {
        color: #ff7e5d;
      }
      .bg-main {
        background-color: #ff7e5d;
      }
      .tab-bar {
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
        height: 80px;
        background-color: rgba(255, 255, 255, 0.95);
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #e5e5e5;
        padding-bottom: 20px;
      }
      .tab-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 10px;
      }
      .tab-icon {
        font-size: 24px;
        margin-bottom: 4px;
      }
      .active {
        color: #ff7e5d;
      }
      .item-card {
        border-radius: 16px;
        overflow: hidden;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
      }
      .story-badge {
        background-color: rgba(255, 126, 93, 0.9);
        border-radius: 12px;
        padding: 2px 8px;
        color: white;
        font-size: 10px;
        position: absolute;
        top: 10px;
        right: 10px;
      }
    </style>
  </head>
  <body class="bg-gray-50">
    <div class="p-4 pt-12">
      <div class="flex justify-between items-center mb-6">
        <h1 class="text-2xl font-bold">我的物品</h1>
        <div class="flex space-x-3">
          <button class="bg-gray-100 rounded-full w-10 h-10 flex items-center justify-center">
            <i class="fas fa-search text-gray-500"></i>
          </button>
          <button class="bg-gray-100 rounded-full w-10 h-10 flex items-center justify-center">
            <i class="fas fa-bell text-gray-500"></i>
          </button>
        </div>
      </div>

      <div class="flex space-x-2 mb-6 overflow-x-auto py-1">
        <button class="bg-main text-white px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">全部物品</button>
        <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200">电子产品</button>
        <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200">服装</button>
        <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200">收藏品</button>
        <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200">家居</button>
      </div>

      <div class="grid grid-cols-2 gap-4 mb-6">
        <div class="item-card bg-white">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1606041008023-472dfb5e530f" alt="复古相机" class="w-full h-40 object-cover" />
            <div class="story-badge">
              <i class="fas fa-book-open mr-1"></i>
              故事
            </div>
          </div>
          <div class="p-3">
            <h3 class="font-medium text-gray-900">复古相机</h3>
            <div class="flex justify-between items-center mt-1">
              <div class="text-xs text-gray-500">收藏品</div>
              <div class="flex">
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-gray-300 text-xs"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="item-card bg-white">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1600185365483-26d7a4cc7519" alt="机械键盘" class="w-full h-40 object-cover" />
          </div>
          <div class="p-3">
            <h3 class="font-medium text-gray-900">机械键盘</h3>
            <div class="flex justify-between items-center mt-1">
              <div class="text-xs text-gray-500">电子产品</div>
              <div class="flex">
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-gray-300 text-xs"></i>
                <i class="fas fa-star text-gray-300 text-xs"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="item-card bg-white">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1560243563-062bfc001d68" alt="运动鞋" class="w-full h-40 object-cover" />
            <div class="story-badge">
              <i class="fas fa-book-open mr-1"></i>
              故事
            </div>
          </div>
          <div class="p-3">
            <h3 class="font-medium text-gray-900">运动鞋</h3>
            <div class="flex justify-between items-center mt-1">
              <div class="text-xs text-gray-500">服装</div>
              <div class="flex">
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
              </div>
            </div>
          </div>
        </div>

        <div class="item-card bg-white">
          <div class="relative">
            <img src="https://images.unsplash.com/photo-1542291026-7eec264c27ff" alt="手表" class="w-full h-40 object-cover" />
          </div>
          <div class="p-3">
            <h3 class="font-medium text-gray-900">手表</h3>
            <div class="flex justify-between items-center mt-1">
              <div class="text-xs text-gray-500">配饰</div>
              <div class="flex">
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-yellow-400 text-xs"></i>
                <i class="fas fa-star text-gray-300 text-xs"></i>
              </div>
            </div>
          </div>
        </div>
      </div>

      <h2 class="text-lg font-semibold mb-4">最近添加</h2>
      <div class="overflow-x-auto">
        <div class="flex space-x-4 pb-2">
          <div class="flex-shrink-0 w-32">
            <div class="bg-white rounded-lg overflow-hidden shadow">
              <img src="https://images.unsplash.com/photo-1511499767150-a48a237f0083" alt="耳机" class="w-full h-32 object-cover" />
              <div class="p-2">
                <div class="text-sm font-medium">耳机</div>
                <div class="text-xs text-gray-500">电子产品</div>
              </div>
            </div>
          </div>

          <div class="flex-shrink-0 w-32">
            <div class="bg-white rounded-lg overflow-hidden shadow">
              <img src="https://images.unsplash.com/photo-1434389677669-e08b4cac3105" alt="相册" class="w-full h-32 object-cover" />
              <div class="p-2">
                <div class="text-sm font-medium">相册</div>
                <div class="text-xs text-gray-500">收藏品</div>
              </div>
            </div>
          </div>

          <div class="flex-shrink-0 w-32">
            <div class="bg-white rounded-lg overflow-hidden shadow">
              <img src="https://images.unsplash.com/photo-1491553895911-0055eca6402d" alt="球鞋" class="w-full h-32 object-cover" />
              <div class="p-2">
                <div class="text-sm font-medium">球鞋</div>
                <div class="text-xs text-gray-500">服装</div>
              </div>
            </div>
          </div>

          <div class="flex-shrink-0 w-32">
            <div class="bg-white rounded-lg overflow-hidden shadow">
              <img src="https://images.unsplash.com/photo-1517420704952-d9f39e95b43e" alt="手办" class="w-full h-32 object-cover" />
              <div class="p-2">
                <div class="text-sm font-medium">手办</div>
                <div class="text-xs text-gray-500">收藏品</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部导航栏 -->
    <div class="tab-bar">
      <div class="tab-item active">
        <i class="fas fa-home tab-icon"></i>
        <span>首页</span>
      </div>
      <div class="tab-item">
        <i class="fas fa-cube tab-icon"></i>
        <span>展示</span>
      </div>
      <div class="tab-item">
        <div class="bg-main rounded-full w-14 h-14 flex items-center justify-center -mt-5">
          <i class="fas fa-plus text-white text-xl"></i>
        </div>
      </div>
      <div class="tab-item">
        <i class="fas fa-users tab-icon"></i>
        <span>社区</span>
      </div>
      <div class="tab-item">
        <i class="fas fa-user tab-icon"></i>
        <span>我的</span>
      </div>
    </div>
  </body>
</html>
