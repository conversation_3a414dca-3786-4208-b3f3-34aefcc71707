<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>记忆博物馆 - 原型展示</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet" />
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css" />
    <style>
      :root {
        --primary: #5d8bf4;
        --secondary: #ff7e5d;
      }
      .phone-frame {
        width: 390px;
        height: 844px;
        border-radius: 40px;
        overflow: hidden;
        box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
        margin: 2rem;
        position: relative;
        background: white;
      }
      .status-bar {
        height: 44px;
        background: white;
        display: flex;
        justify-content: space-between;
        padding: 0 1.5rem;
        align-items: center;
        font-weight: 600;
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        z-index: 20;
      }
      .screen-content {
        height: 100%;
        overflow-y: auto;
        padding-top: 44px;
        padding-bottom: 80px;
      }
      .nav-bar {
        height: 80px;
        background: white;
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        display: flex;
        justify-content: space-around;
        align-items: center;
        border-top: 1px solid #eee;
      }
      .nav-item {
        display: flex;
        flex-direction: column;
        align-items: center;
        font-size: 0.75rem;
      }
      .nav-icon {
        font-size: 1.5rem;
        margin-bottom: 0.25rem;
      }
      .active {
        color: var(--primary);
      }
      .story-card {
        border-radius: 16px;
        overflow: hidden;
        transition: transform 0.3s ease;
      }
      .story-card:hover {
        transform: translateY(-5px);
      }
      .collab-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: rgba(255, 255, 255, 0.9);
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        display: flex;
        align-items: center;
      }
      .ai-tag {
        background: linear-gradient(45deg, #ff7e5d, #5d8bf4);
        color: white;
        font-size: 0.75rem;
      }
      .collab-card {
        border-radius: 16px;
        overflow: hidden;
        transition: all 0.3s ease;
      }
      .collab-card:hover {
        transform: translateY(-3px);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
      }
      .editor-header {
        position: sticky;
        top: 0;
        z-index: 10;
        background: white;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
      }
      .timeline-node {
        position: relative;
        padding-left: 30px;
        margin-bottom: 20px;
      }
      .timeline-node::before {
        content: "";
        position: absolute;
        left: 0;
        top: 5px;
        width: 16px;
        height: 16px;
        border-radius: 50%;
        background: var(--primary);
      }
      .timeline-node::after {
        content: "";
        position: absolute;
        left: 7px;
        top: 21px;
        bottom: -25px;
        width: 2px;
        background: #e0e0e0;
      }
      .timeline-node:last-child::after {
        display: none;
      }
      .contributor-badge {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        border: 2px solid white;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }
      .ai-suggestion {
        border-left: 3px solid var(--secondary);
        background: rgba(255, 126, 93, 0.05);
      }
      .premium-badge {
        background: linear-gradient(45deg, #ffd700, #ff7e5d);
        color: white;
      }
      .stat-card {
        background: white;
        border-radius: 16px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      }
      .menu-item {
        padding: 16px 0;
        border-bottom: 1px solid #f0f0f0;
      }
      .menu-item:last-child {
        border-bottom: none;
      }
    </style>
  </head>
  <body class="bg-gray-100 p-4">
    <h1 class="text-3xl font-bold text-center mb-8 text-gray-800">记忆博物馆 · 原型设计</h1>

    <div class="flex flex-wrap justify-center">
      <!-- 首页 -->
      <div class="phone-frame">
        <div class="status-bar">
          <div>9:41</div>
          <div class="flex gap-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>
        <div class="screen-content">
          <div class="p-4">
            <h1 class="text-2xl font-bold mb-6">我的记忆收藏</h1>

            <!-- 故事卡片 -->
            <div class="grid grid-cols-2 gap-4 mb-6">
              <div class="story-card relative">
                <img src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f" alt="爷爷的怀表" class="w-full h-48 object-cover" />
                <div class="collab-badge">
                  <i class="fas fa-users mr-1"></i>
                  12人协作
                </div>
                <div class="p-3 bg-white">
                  <h3 class="font-semibold">爷爷的怀表</h3>
                  <p class="text-sm text-gray-500 mt-1 line-clamp-2">1945年购于上海，见证抗战胜利的历史时刻...</p>
                </div>
              </div>

              <div class="story-card relative">
                <img src="https://images.unsplash.com/photo-1544787219-7f47ccb76574" alt="旅行日记" class="w-full h-48 object-cover" />
                <div class="absolute top-4 right-4 ai-tag px-2 py-1 rounded-full">AI美化</div>
                <div class="p-3 bg-white">
                  <h3 class="font-semibold">环球旅行日记</h3>
                  <p class="text-sm text-gray-500 mt-1 line-clamp-2">30个国家旅行记录，包含手绘地图和当地邮票...</p>
                </div>
              </div>
            </div>

            <!-- 协作专区 -->
            <div class="mb-6">
              <div class="flex justify-between items-center mb-3">
                <h2 class="text-xl font-bold">正在协作的故事</h2>
                <a href="#" class="text-blue-500 text-sm">查看全部</a>
              </div>
              <div class="bg-white rounded-xl p-4">
                <div class="flex items-center mb-3">
                  <div class="w-12 h-12 rounded-full overflow-hidden mr-3">
                    <img src="https://images.unsplash.com/photo-1534528741775-53994a69daeb" alt="用户" class="w-full h-full object-cover" />
                  </div>
                  <div>
                    <h3 class="font-semibold">《凡人修仙传》手稿</h3>
                    <p class="text-xs text-gray-500">3人正在编辑</p>
                  </div>
                </div>
                <div class="text-sm text-gray-700 mb-3">网络文学史上重要作品的原稿，记录创作过程中的修改痕迹...</div>
                <button class="w-full bg-blue-100 text-blue-600 py-2 rounded-lg font-medium">
                  <i class="fas fa-edit mr-2"></i>
                  参与编辑
                </button>
              </div>
            </div>

            <!-- AI美化推荐 -->
            <div class="mb-6">
              <h2 class="text-xl font-bold mb-3">AI美化推荐</h2>
              <div class="flex overflow-x-auto pb-2 space-x-3">
                <div class="flex-shrink-0 w-32">
                  <div class="relative">
                    <img src="https://images.unsplash.com/photo-1548032885-b5e38734688a" alt="古钱币" class="w-full h-32 rounded-xl object-cover" />
                    <div class="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">3D展示</div>
                  </div>
                  <p class="text-xs mt-2 text-center">战国刀币</p>
                </div>
                <div class="flex-shrink-0 w-32">
                  <div class="relative">
                    <img src="https://images.unsplash.com/photo-1589998059171-988d887df646" alt="书信" class="w-full h-32 rounded-xl object-cover" />
                    <div class="absolute bottom-2 left-2 bg-black bg-opacity-50 text-white text-xs px-2 py-1 rounded">色彩修复</div>
                  </div>
                  <p class="text-xs mt-2 text-center">家书</p>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="nav-bar">
          <div class="nav-item active">
            <i class="fas fa-home nav-icon"></i>
            <span>首页</span>
          </div>
          <div class="nav-item">
            <i class="fas fa-compass nav-icon"></i>
            <span>发现</span>
          </div>
          <div class="nav-item">
            <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
              <i class="fas fa-plus"></i>
            </div>
          </div>
          <div class="nav-item">
            <i class="fas fa-users nav-icon"></i>
            <span>协作</span>
          </div>
          <div class="nav-item">
            <i class="fas fa-user nav-icon"></i>
            <span>我的</span>
          </div>
        </div>
      </div>

      <!-- 故事编辑页 -->
      <div class="phone-frame">
        <div class="status-bar">
          <div>9:41</div>
          <div class="flex gap-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>
        <div class="screen-content">
          <div class="p-4">
            <div class="flex justify-between items-center mb-6">
              <button class="text-blue-500">
                <i class="fas fa-arrow-left"></i>
              </button>
              <h1 class="text-xl font-bold">故事编辑器</h1>
              <button class="text-blue-500 font-medium">发布</button>
            </div>

            <div class="mb-6">
              <input type="text" placeholder="故事标题" class="w-full text-2xl font-bold border-0 focus:ring-0 py-2" />
              <div class="flex items-center text-sm text-gray-500 mt-2">
                <i class="fas fa-users mr-1"></i>
                <span>协作模式：公开编辑</span>
              </div>
            </div>

            <div class="mb-4">
              <div class="flex justify-between mb-2">
                <label class="font-medium">时间线</label>
                <button class="text-blue-500 text-sm">
                  <i class="fas fa-plus mr-1"></i>
                  添加节点
                </button>
              </div>
              <div class="space-y-3">
                <div class="flex items-start">
                  <div class="bg-blue-100 text-blue-800 w-8 h-8 rounded-full flex items-center justify-center mr-3 mt-1">1</div>
                  <div class="flex-1">
                    <input type="text" placeholder="时间点" class="w-full font-medium mb-1" />
                    <textarea placeholder="事件描述" rows="2" class="w-full text-sm"></textarea>
                  </div>
                </div>
              </div>
            </div>

            <div class="mb-4">
              <label class="font-medium block mb-2">添加内容</label>
              <div class="grid grid-cols-4 gap-2 mb-3">
                <button class="border rounded-lg py-2 flex flex-col items-center justify-center">
                  <i class="fas fa-font text-lg mb-1"></i>
                  <span class="text-xs">文字</span>
                </button>
                <button class="border rounded-lg py-2 flex flex-col items-center justify-center">
                  <i class="fas fa-image text-lg mb-1"></i>
                  <span class="text-xs">图片</span>
                </button>
                <button class="border rounded-lg py-2 flex flex-col items-center justify-center">
                  <i class="fas fa-film text-lg mb-1"></i>
                  <span class="text-xs">视频</span>
                </button>
                <button class="border rounded-lg py-2 flex flex-col items-center justify-center">
                  <i class="fas fa-microphone text-lg mb-1"></i>
                  <span class="text-xs">录音</span>
                </button>
              </div>
            </div>

            <div class="mb-4">
              <label class="font-medium block mb-2">AI助手</label>
              <div class="bg-blue-50 rounded-xl p-3">
                <div class="flex items-start mb-2">
                  <div class="bg-blue-100 rounded-full w-8 h-8 flex items-center justify-center mr-2">
                    <i class="fas fa-robot text-blue-600"></i>
                  </div>
                  <div class="flex-1">
                    <p class="text-sm">需要我帮你润色这段描述吗？或者生成时间线建议？</p>
                  </div>
                </div>
                <button class="w-full bg-blue-500 text-white py-2 rounded-lg font-medium">使用AI优化</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 3D展示页 -->
      <div class="phone-frame">
        <div class="status-bar">
          <div>9:41</div>
          <div class="flex gap-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>
        <div class="screen-content bg-black">
          <div class="relative h-full">
            <!-- 3D展示区 -->
            <div class="absolute inset-0 flex items-center justify-center">
              <div class="bg-gray-900 border-2 border-blue-500 rounded-xl w-64 h-64 flex items-center justify-center">
                <div class="text-center text-white">
                  <i class="fas fa-cube text-4xl mb-3"></i>
                  <p>3D物品展示</p>
                  <p class="text-sm opacity-75 mt-1">旋转/缩放查看细节</p>
                </div>
              </div>
            </div>

            <!-- 控制面板 -->
            <div class="absolute bottom-6 left-0 right-0">
              <div class="flex justify-center space-x-4">
                <button class="w-12 h-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center text-white">
                  <i class="fas fa-camera"></i>
                </button>
                <button class="w-12 h-12 rounded-full bg-blue-500 flex items-center justify-center text-white">
                  <i class="fas fa-vr-cardboard"></i>
                </button>
                <button class="w-12 h-12 rounded-full bg-white bg-opacity-20 flex items-center justify-center text-white">
                  <i class="fas fa-magic"></i>
                </button>
              </div>
            </div>

            <!-- 故事卡片 -->
            <div class="absolute bottom-32 left-4 right-4 bg-white bg-opacity-90 backdrop-blur rounded-xl p-4">
              <h3 class="font-bold mb-2">明代青花瓷</h3>
              <p class="text-sm line-clamp-2">此瓷器为景德镇官窑出品，纹饰描绘鲤鱼跃龙门场景，象征科举及第...</p>
              <div class="flex justify-between items-center mt-3">
                <div class="text-xs text-gray-500">
                  <i class="fas fa-history mr-1"></i>
                  7人协作完善
                </div>
                <button class="text-blue-500 text-sm font-medium">
                  查看完整故事
                  <i class="fas fa-arrow-right ml-1"></i>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 社区发现页 -->
      <div class="phone-frame">
        <div class="status-bar">
          <div>9:41</div>
          <div class="flex gap-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>
        <div class="screen-content">
          <div class="p-4">
            <h1 class="text-2xl font-bold mb-6">发现美好记忆</h1>

            <div class="flex space-x-2 mb-4 overflow-x-auto">
              <button class="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">全部</button>
              <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">传家宝</button>
              <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">旅行记忆</button>
              <button class="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">手工艺品</button>
            </div>

            <div class="space-y-6">
              <!-- 社区故事卡片 -->
              <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                <img src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f" alt="怀表" class="w-full h-48 object-cover" />
                <div class="p-4">
                  <div class="flex justify-between items-start mb-2">
                    <h3 class="font-bold text-lg">战火中的怀表</h3>
                    <div class="flex items-center text-yellow-400">
                      <i class="fas fa-star"></i>
                      <span class="ml-1">4.8</span>
                    </div>
                  </div>
                  <p class="text-gray-600 text-sm mb-3">这块怀表陪伴祖父度过抗战岁月，表盖弹痕记录着1943年的空袭...</p>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center">
                      <div class="w-6 h-6 rounded-full overflow-hidden mr-2">
                        <img src="https://images.unsplash.com/photo-1494790108377-be9c29b29330" alt="用户" class="w-full h-full object-cover" />
                      </div>
                      <span class="text-xs">李思思</span>
                    </div>
                    <div class="text-xs text-gray-500">
                      <i class="fas fa-users mr-1"></i>
                      8人协作
                    </div>
                  </div>
                </div>
              </div>

              <div class="bg-white rounded-xl overflow-hidden shadow-sm">
                <div class="relative">
                  <img src="https://images.unsplash.com/photo-1544787219-7f47ccb76574" alt="日记" class="w-full h-48 object-cover" />
                  <div class="absolute top-4 right-4 ai-tag px-2 py-1 rounded-full">AI复原</div>
                </div>
                <div class="p-4">
                  <div class="flex justify-between items-start mb-2">
                    <h3 class="font-bold text-lg">南极探险日记</h3>
                    <div class="flex items-center text-yellow-400">
                      <i class="fas fa-star"></i>
                      <span class="ml-1">4.9</span>
                    </div>
                  </div>
                  <p class="text-gray-600 text-sm mb-3">1921年南极科考队成员的日记，记录与企鹅相遇的奇妙时刻...</p>
                  <div class="flex justify-between items-center">
                    <div class="flex items-center">
                      <div class="w-6 h-6 rounded-full overflow-hidden mr-2">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d" alt="用户" class="w-full h-full object-cover" />
                      </div>
                      <span class="text-xs">王科考</span>
                    </div>
                    <div class="text-xs text-gray-500">
                      <i class="fas fa-history mr-1"></i>
                      3天前更新
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="nav-bar">
          <div class="nav-item">
            <i class="fas fa-home nav-icon"></i>
            <span>首页</span>
          </div>
          <div class="nav-item active">
            <i class="fas fa-compass nav-icon"></i>
            <span>发现</span>
          </div>
          <div class="nav-item">
            <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
              <i class="fas fa-plus"></i>
            </div>
          </div>
          <div class="nav-item">
            <i class="fas fa-users nav-icon"></i>
            <span>协作</span>
          </div>
          <div class="nav-item">
            <i class="fas fa-user nav-icon"></i>
            <span>我的</span>
          </div>
        </div>
      </div>

      <!-- 协作编辑页 -->
      <div class="phone-frame">
        <div class="status-bar">
          <div>9:41</div>
          <div class="flex gap-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>
        <div class="screen-content">
          <div class="p-4">
            <div class="flex items-center justify-between mb-6">
              <h1 class="text-2xl font-bold">协作编辑</h1>
              <div class="flex space-x-3">
                <button class="bg-gray-100 rounded-full w-10 h-10 flex items-center justify-center">
                  <i class="fas fa-search text-gray-500"></i>
                </button>
                <button class="bg-gray-100 rounded-full w-10 h-10 flex items-center justify-center">
                  <i class="fas fa-sliders-h text-gray-500"></i>
                </button>
              </div>
            </div>

            <!-- 协作分类 -->
            <div class="flex space-x-2 mb-6 overflow-x-auto py-1">
              <button class="bg-blue-500 text-white px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap">全部</button>
              <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200">历史文物</button>
              <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200">家族传承</button>
              <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200">文学手稿</button>
              <button class="bg-white text-gray-700 px-4 py-2 rounded-full text-sm font-medium whitespace-nowrap border border-gray-200">艺术创作</button>
            </div>

            <!-- 热门协作 -->
            <h2 class="text-xl font-bold mb-4">热门协作项目</h2>
            <div class="grid grid-cols-2 gap-4 mb-6">
              <div class="collab-card bg-white">
                <div class="relative">
                  <img src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f" alt="爷爷的怀表" class="w-full h-40 object-cover" />
                  <div class="absolute top-3 right-3 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">12人参与</div>
                </div>
                <div class="p-3">
                  <h3 class="font-semibold mb-1">爷爷的怀表</h3>
                  <p class="text-sm text-gray-500 line-clamp-2">1945年购于上海，见证抗战胜利的历史时刻...</p>
                  <div class="flex mt-2 -space-x-2">
                    <img src="https://randomuser.me/api/portraits/women/12.jpg" class="contributor-badge" />
                    <img src="https://randomuser.me/api/portraits/men/32.jpg" class="contributor-badge" />
                    <img src="https://randomuser.me/api/portraits/women/44.jpg" class="contributor-badge" />
                    <div class="contributor-badge bg-blue-100 flex items-center justify-center">
                      <span class="text-xs text-blue-500">+9</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="collab-card bg-white">
                <div class="relative">
                  <img src="https://images.unsplash.com/photo-1544787219-7f47ccb76574" alt="旅行日记" class="w-full h-40 object-cover" />
                  <div class="absolute top-3 right-3 bg-blue-500 text-white text-xs px-2 py-1 rounded-full">8人参与</div>
                </div>
                <div class="p-3">
                  <h3 class="font-semibold mb-1">环球旅行日记</h3>
                  <p class="text-sm text-gray-500 line-clamp-2">30个国家旅行记录，包含手绘地图和当地邮票...</p>
                  <div class="flex mt-2 -space-x-2">
                    <img src="https://randomuser.me/api/portraits/men/22.jpg" class="contributor-badge" />
                    <img src="https://randomuser.me/api/portraits/women/33.jpg" class="contributor-badge" />
                    <div class="contributor-badge bg-blue-100 flex items-center justify-center">
                      <span class="text-xs text-blue-500">+6</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 协作编辑器 -->
            <h2 class="text-xl font-bold mb-4">正在编辑</h2>
            <div class="bg-white rounded-xl p-4 mb-6">
              <div class="flex items-center mb-4">
                <div class="w-12 h-12 rounded-full overflow-hidden mr-3">
                  <img src="https://randomuser.me/api/portraits/women/68.jpg" alt="用户" class="w-full h-full object-cover" />
                </div>
                <div>
                  <h3 class="font-semibold">《凡人修仙传》手稿</h3>
                  <p class="text-xs text-gray-500">最后编辑：2小时前</p>
                </div>
              </div>

              <div class="mb-4">
                <label class="block text-sm font-medium mb-2">故事时间线</label>
                <div class="timeline-node">
                  <div class="font-medium mb-1">2009年 · 创作起源</div>
                  <div class="text-sm bg-gray-50 p-3 rounded-lg">忘语在起点中文网开始连载，最初的手稿包含大量修改痕迹...</div>
                </div>

                <div class="timeline-node">
                  <div class="font-medium mb-1">2010年 · 首次出版</div>
                  <div class="text-sm bg-gray-50 p-3 rounded-lg">实体书首版印刷，封面由知名插画师绘制...</div>
                </div>

                <div class="flex items-center text-blue-500 mt-2">
                  <i class="fas fa-plus mr-1"></i>
                  <span>添加时间节点</span>
                </div>
              </div>

              <div class="mb-4">
                <label class="block text-sm font-medium mb-2">AI建议</label>
                <div class="ai-suggestion p-3 rounded-lg mb-2">
                  <div class="flex items-start">
                    <div class="bg-orange-100 rounded-full w-6 h-6 flex items-center justify-center mr-2 mt-1">
                      <i class="fas fa-robot text-orange-500 text-xs"></i>
                    </div>
                    <div>
                      <p class="text-sm">建议补充：2012年改编漫画的原始手稿扫描件</p>
                    </div>
                  </div>
                </div>
              </div>

              <button class="w-full bg-blue-500 text-white py-3 rounded-lg font-medium">
                <i class="fas fa-save mr-2"></i>
                保存修改
              </button>
            </div>
          </div>

          <!-- 底部导航 -->
          <div class="fixed bottom-0 left-0 right-0 h-20 bg-white flex justify-around items-center border-t border-gray-100">
            <div class="flex flex-col items-center text-blue-500">
              <i class="fas fa-home text-xl mb-1"></i>
              <span class="text-xs">首页</span>
            </div>
            <div class="flex flex-col items-center text-blue-500">
              <i class="fas fa-compass text-xl mb-1"></i>
              <span class="text-xs">发现</span>
            </div>
            <div class="flex flex-col items-center">
              <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
                <i class="fas fa-plus"></i>
              </div>
            </div>
            <div class="flex flex-col items-center text-blue-500 font-bold">
              <i class="fas fa-users text-xl mb-1"></i>
              <span class="text-xs">协作</span>
            </div>
            <div class="flex flex-col items-center text-gray-400">
              <i class="fas fa-user text-xl mb-1"></i>
              <span class="text-xs">我的</span>
            </div>
          </div>
        </div>
        <div class="nav-bar">
          <div class="nav-item">
            <i class="fas fa-home nav-icon"></i>
            <span>首页</span>
          </div>
          <div class="nav-item active">
            <i class="fas fa-compass nav-icon"></i>
            <span>发现</span>
          </div>
          <div class="nav-item">
            <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
              <i class="fas fa-plus"></i>
            </div>
          </div>
          <div class="nav-item">
            <i class="fas fa-users nav-icon"></i>
            <span>协作</span>
          </div>
          <div class="nav-item">
            <i class="fas fa-user nav-icon"></i>
            <span>我的</span>
          </div>
        </div>
      </div>

      <!-- 个人中心页 -->
      <div class="phone-frame">
        <div class="status-bar">
          <div>9:41</div>
          <div class="flex gap-2">
            <i class="fas fa-signal"></i>
            <i class="fas fa-wifi"></i>
            <i class="fas fa-battery-three-quarters"></i>
          </div>
        </div>
        <div class="screen-content">
          <div class="p-4">
            <!-- 用户信息 -->
            <div class="flex items-center mb-8">
              <div class="relative mr-4">
                <img src="https://randomuser.me/api/portraits/women/68.jpg" class="w-16 h-16 rounded-full object-cover border-2 border-white shadow" />
                <div class="absolute bottom-0 right-0 bg-blue-500 text-white w-6 h-6 rounded-full flex items-center justify-center">
                  <i class="fas fa-pencil-alt text-xs"></i>
                </div>
              </div>
              <div class="flex-1">
                <h1 class="text-xl font-bold">张小雨</h1>
                <p class="text-gray-500 text-sm">收藏家 · 历史爱好者</p>
              </div>
              <div class="premium-badge px-3 py-1 rounded-full text-xs font-bold">
                <i class="fas fa-crown mr-1"></i>
                高级会员
              </div>
            </div>

            <!-- 数据统计 -->
            <div class="grid grid-cols-3 gap-3 mb-6">
              <div class="stat-card p-3 text-center">
                <div class="text-2xl font-bold text-blue-500">42</div>
                <div class="text-xs text-gray-500">收藏物品</div>
              </div>
              <div class="stat-card p-3 text-center">
                <div class="text-2xl font-bold text-blue-500">18</div>
                <div class="text-xs text-gray-500">创作故事</div>
              </div>
              <div class="stat-card p-3 text-center">
                <div class="text-2xl font-bold text-blue-500">127</div>
                <div class="text-xs text-gray-500">协作贡献</div>
              </div>
            </div>

            <!-- 我的物品 -->
            <div class="mb-6">
              <div class="flex justify-between items-center mb-3">
                <h2 class="text-lg font-bold">我的收藏</h2>
                <a href="#" class="text-blue-500 text-sm">查看全部</a>
              </div>
              <div class="grid grid-cols-3 gap-2">
                <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  <img src="https://images.unsplash.com/photo-1589829545856-d10d557cf95f" class="w-full h-full object-cover" />
                </div>
                <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  <img src="https://images.unsplash.com/photo-1544787219-7f47ccb76574" class="w-full h-full object-cover" />
                </div>
                <div class="aspect-square bg-gray-100 rounded-lg overflow-hidden relative">
                  <img src="https://images.unsplash.com/photo-1589998059171-988d887df646" class="w-full h-full object-cover" />
                  <div class="absolute top-2 right-2 bg-blue-500 text-white text-xs px-1 rounded">AI</div>
                </div>
              </div>
            </div>

            <!-- 功能菜单 -->
            <div class="bg-white rounded-xl overflow-hidden mb-6">
              <div class="menu-item flex justify-between items-center px-4">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-history text-blue-500"></i>
                  </div>
                  <span>编辑历史</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
              </div>

              <div class="menu-item flex justify-between items-center px-4">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-orange-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-robot text-orange-500"></i>
                  </div>
                  <span>AI工具包</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
              </div>

              <div class="menu-item flex justify-between items-center px-4">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-cube text-purple-500"></i>
                  </div>
                  <span>3D展厅</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
              </div>

              <div class="menu-item flex justify-between items-center px-4">
                <div class="flex items-center">
                  <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center mr-3">
                    <i class="fas fa-lock text-green-500"></i>
                  </div>
                  <span>隐私设置</span>
                </div>
                <i class="fas fa-chevron-right text-gray-400"></i>
              </div>
            </div>

            <!-- 会员状态 -->
            <div class="bg-gradient-to-r from-blue-500 to-purple-500 rounded-xl p-4 text-white mb-6">
              <div class="flex justify-between items-start">
                <div>
                  <h3 class="font-bold mb-1">高级会员</h3>
                  <p class="text-sm opacity-90">有效期至 2026/03/15</p>
                </div>
                <div class="bg-white text-blue-500 px-3 py-1 rounded-full text-xs font-bold">已启用</div>
              </div>
              <div class="mt-4 grid grid-cols-2 gap-2">
                <div class="flex items-center">
                  <i class="fas fa-check-circle mr-2"></i>
                  <span class="text-xs">无限3D模型</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-check-circle mr-2"></i>
                  <span class="text-xs">AI美化工具</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-check-circle mr-2"></i>
                  <span class="text-xs">AR展示</span>
                </div>
                <div class="flex items-center">
                  <i class="fas fa-check-circle mr-2"></i>
                  <span class="text-xs">高级模板</span>
                </div>
              </div>
              <button class="w-full bg-white text-blue-500 py-2 rounded-lg font-medium mt-4">管理订阅</button>
            </div>
          </div>

          <!-- 底部导航 -->
          <div class="fixed bottom-0 left-0 right-0 h-20 bg-white flex justify-around items-center border-t border-gray-100">
            <div class="flex flex-col items-center text-blue-500">
              <i class="fas fa-home text-xl mb-1"></i>
              <span class="text-xs">首页</span>
            </div>
            <div class="flex flex-col items-center text-blue-500">
              <i class="fas fa-compass text-xl mb-1"></i>
              <span class="text-xs">发现</span>
            </div>
            <div class="flex flex-col items-center">
              <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
                <i class="fas fa-plus"></i>
              </div>
            </div>
            <div class="flex flex-col items-center text-blue-500 font-bold">
              <i class="fas fa-users text-xl mb-1"></i>
              <span class="text-xs">协作</span>
            </div>
            <div class="flex flex-col items-center text-gray-400">
              <i class="fas fa-user text-xl mb-1"></i>
              <span class="text-xs">我的</span>
            </div>
          </div>
        </div>
        <div class="nav-bar">
          <div class="nav-item">
            <i class="fas fa-home nav-icon"></i>
            <span>首页</span>
          </div>
          <div class="nav-item active">
            <i class="fas fa-compass nav-icon"></i>
            <span>发现</span>
          </div>
          <div class="nav-item">
            <div class="bg-blue-500 text-white w-12 h-12 rounded-full flex items-center justify-center -mt-6">
              <i class="fas fa-plus"></i>
            </div>
          </div>
          <div class="nav-item">
            <i class="fas fa-users nav-icon"></i>
            <span>协作</span>
          </div>
          <div class="nav-item">
            <i class="fas fa-user nav-icon"></i>
            <span>我的</span>
          </div>
        </div>
      </div>
    </div>
  </body>
</html>
