import * as ImagePicker from "expo-image-picker";
import { Alert } from "react-native";

export interface ImagePickerOptions {
  allowsEditing?: boolean;
  aspect?: [number, number];
  quality?: number;
  allowsMultipleSelection?: boolean;
  selectionLimit?: number;
}

export const requestPermissions = async () => {
  // Request camera permissions
  const cameraPermission = await ImagePicker.requestCameraPermissionsAsync();
  if (cameraPermission.status !== "granted") {
    Alert.alert("权限需要", "需要相机权限才能拍照");
    return false;
  }

  // Request media library permissions
  const mediaLibraryPermission = await ImagePicker.requestMediaLibraryPermissionsAsync();
  if (mediaLibraryPermission.status !== "granted") {
    Alert.alert("权限需要", "需要相册权限才能选择图片");
    return false;
  }

  return true;
};

export const takePhoto = async (options: ImagePickerOptions = {}): Promise<string | null> => {
  try {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return null;

    const result = await ImagePicker.launchCameraAsync({
      mediaTypes: "images",
      allowsEditing: options.allowsEditing ?? true,
      aspect: options.aspect ?? [16, 9],
      quality: options.quality ?? 0.8,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      return result.assets[0].uri;
    }

    return null;
  } catch (error) {
    console.error("Error taking photo:", error);
    Alert.alert("错误", "拍照时发生错误，请重试");
    return null;
  }
};

export const pickFromGallery = async (options: ImagePickerOptions = {}): Promise<string | null> => {
  try {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return null;

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: "images",
      allowsEditing: options.allowsEditing ?? true,
      aspect: options.aspect ?? [16, 9],
      quality: options.quality ?? 0.8,
      allowsMultipleSelection: options.allowsMultipleSelection ?? false,
    });

    if (!result.canceled && result.assets && result.assets.length > 0) {
      return result.assets[0].uri;
    }

    return null;
  } catch (error) {
    console.error("Error picking from gallery:", error);
    Alert.alert("错误", "选择图片时发生错误，请重试");
    return null;
  }
};

export const pickMultipleFromGallery = async (
  options: ImagePickerOptions = {}
): Promise<string[]> => {
  try {
    const hasPermission = await requestPermissions();
    if (!hasPermission) return [];

    const result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ImagePicker.MediaTypeOptions.Images,
      allowsEditing: false, // Disable editing for multiple selection
      quality: options.quality ?? 0.8,
      allowsMultipleSelection: true,
      selectionLimit: options.selectionLimit,
    });
    console.log(result, "result");

    if (!result.canceled && result.assets && result.assets.length > 0) {
      return result.assets.map(asset => asset.uri);
    }

    return [];
  } catch (error) {
    console.error("Error picking multiple from gallery:", error);
    Alert.alert("错误", "选择图片时发生错误，请重试");
    return [];
  }
};
