/**
 * Item Service - High-level item management functions
 * Provides convenient methods for item operations
 */

import { APIResponse, Item } from "@/types";
import {
  deleteDraft,
  deleteItem,
  generateId,
  getDraftById,
  getItemById,
  loadDrafts,
  loadStories,
  publishDraft,
  saveItem,
} from "./storage";

// ============================================================================
// Item List Management
// ============================================================================

/**
 * Get all published stories
 */
export const getAllStories = async (): Promise<APIResponse<Item[]>> => {
  try {
    const response = await loadStories();
    if (response.success) {
      // Filter out drafts and sort by updatedAt
      const publishedStories = (response.data || []).sort((a, b) => {
        const dateA = a.updatedAt ? new Date(a.updatedAt).getTime() : 0;
        const dateB = b.updatedAt ? new Date(b.updatedAt).getTime() : 0;
        return dateB - dateA;
      });

      return {
        success: true,
        data: publishedStories,
        message: `Found ${publishedStories.length} published stories`,
        timestamp: new Date().toISOString(),
      };
    }
    return response;
  } catch (error) {
    console.error("Error getting all stories:", error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Get all drafts
 */
export const getAllDrafts = async (): Promise<APIResponse<Item[]>> => {
  try {
    const response = await loadDrafts();
    if (response.success) {
      const sortedDrafts = (response.data || []).sort((a, b) => {
        const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
        const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
        return dateB - dateA;
      });

      return {
        success: true,
        data: sortedDrafts,
        message: `Found ${sortedDrafts.length} drafts`,
        timestamp: new Date().toISOString(),
      };
    }
    return response;
  } catch (error) {
    console.error("Error getting all drafts:", error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Search stories by title or content
 */
export const searchStories = async (query: string): Promise<APIResponse<Item[]>> => {
  try {
    const response = await getAllStories();
    if (!response.success) {
      return response;
    }

    const searchTerm = query.toLowerCase().trim();
    if (!searchTerm) {
      return response; // Return all stories if no search term
    }

    const filteredStories = (response.data || []).filter(
      item =>
        (item.title || "").toLowerCase().includes(searchTerm) ||
        (item.content || "").toLowerCase().includes(searchTerm) ||
        (item.tags || []).some(tag => tag.toLowerCase().includes(searchTerm))
    );

    return {
      success: true,
      data: filteredStories,
      message: `Found ${filteredStories.length} stories matching "${query}"`,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error searching stories:", error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Get stories by tag
 */
export const getStoriesByTag = async (tag: string): Promise<APIResponse<Item[]>> => {
  try {
    const response = await getAllStories();
    if (!response.success) {
      return response;
    }

    const filteredStories = (response.data || []).filter(item => (item.tags || []).includes(tag));

    return {
      success: true,
      data: filteredStories,
      message: `Found ${filteredStories.length} stories with tag "${tag}"`,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error getting stories by tag:", error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Get recent stories (last 30 days)
 */
export const getRecentStories = async (days: number = 30): Promise<APIResponse<Item[]>> => {
  try {
    const response = await getAllStories();
    if (!response.success) {
      return response;
    }

    const cutoffDate = new Date();
    cutoffDate.setDate(cutoffDate.getDate() - days);

    const recentStories = (response.data || []).filter(item => {
      if (!item.updatedAt) return false;
      return new Date(item.updatedAt) >= cutoffDate;
    });

    return {
      success: true,
      data: recentStories,
      message: `Found ${recentStories.length} stories from the last ${days} days`,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error getting recent stories:", error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

// ============================================================================
// Item Creation and Management
// ============================================================================

/**
 * Create a new item from scratch
 */
export const createNewItem = async (itemData: Item): Promise<APIResponse<Item>> => {
  try {
    const newItem: Item = {
      ...itemData,
      id: generateId(),
    };

    return await saveItem(newItem);
  } catch (error) {
    console.error("Error creating new item:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Get all unique tags from stories
 */
export const getAllTags = async (): Promise<APIResponse<string[]>> => {
  try {
    const response = await getAllStories();
    if (!response.success) {
      return {
        success: false,
        data: [],
        error: response.error,
        timestamp: new Date().toISOString(),
      };
    }

    const allTags = new Set<string>();
    (response.data || []).forEach(item => {
      (item.tags || []).forEach(tag => allTags.add(tag));
    });

    const sortedTags = Array.from(allTags).sort();

    return {
      success: true,
      data: sortedTags,
      message: `Found ${sortedTags.length} unique tags`,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error getting all tags:", error);
    return {
      success: false,
      data: [],
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

// ============================================================================
// Export convenience functions
// ============================================================================

export {
  deleteDraft,
  deleteItem,
  generateId,
  getDraftById,
  // Re-export storage functions for direct access
  getItemById,
  publishDraft
};

