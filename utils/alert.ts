import { AlertButton } from "@/components/Alert";

// Global state for managing alert
let alertState: {
  visible: boolean;
  title?: string;
  message?: string;
  buttons?: AlertButton[];
  icon?: string;
  iconColor?: string;
  onClose: () => void;
} = {
  visible: false,
  onClose: () => {},
};

let alertUpdateCallback: ((state: typeof alertState) => void) | null = null;

// Register the update callback from the AlertProvider
export const registerAlertCallback = (callback: (state: typeof alertState) => void) => {
  alertUpdateCallback = callback;
};

// Show alert function that mimics Alert.alert API
export const showAlert = (
  title?: string,
  message?: string,
  buttons?: AlertButton[],
  options?: {
    icon?: string;
    iconColor?: string;
  }
) => {
  alertState = {
    visible: true,
    title,
    message,
    buttons: buttons || [{ text: "确定", style: "default" }],
    icon: options?.icon,
    iconColor: options?.iconColor,
    onClose: () => {
      alertState.visible = false;
      if (alertUpdateCallback) {
        alertUpdateCallback({ ...alertState });
      }
    },
  };

  if (alertUpdateCallback) {
    alertUpdateCallback({ ...alertState });
  }
};

// Get current alert state
export const getAlertState = () => ({ ...alertState });

// Hide alert
export const hideAlert = () => {
  alertState.visible = false;
  if (alertUpdateCallback) {
    alertUpdateCallback({ ...alertState });
  }
};
