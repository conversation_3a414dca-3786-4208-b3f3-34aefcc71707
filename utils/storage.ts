/**
 * Local Storage utilities for the Item Memory & Exchange Platform
 * Handles all data persistence using AsyncStorage for React Native
 */

import { APIResponse, Item } from "@/types";
import AsyncStorage from "@react-native-async-storage/async-storage";

// ============================================================================
// Storage Keys
// ============================================================================

export interface StorageKeysTypes {
  STORIES: string;
  DRAFTS: string;
  USER_PREFERENCES: string;
  ITEMS: string;
  POSTS: string;
  CATEGORIES: string;
  USER_PROFILE: string;
  AI_ENHANCEMENTS: string;
  APP_SETTINGS: string;
}

export const STORAGE_KEYS: StorageKeysTypes = {
  STORIES: "@stories",
  DRAFTS: "@item_drafts",
  USER_PREFERENCES: "@user_preferences",
  ITEMS: "@items",
  POSTS: "@posts",
  CATEGORIES: "@categories",
  USER_PROFILE: "@user_profile",
  AI_ENHANCEMENTS: "@ai_enhancements",
  APP_SETTINGS: "@app_settings",
} as const;

// ============================================================================
// Generic Storage Functions
// ============================================================================

/**
 * Generic function to save data to AsyncStorage
 */
export const saveToStorage = async <T>(
  key: StorageKeysTypes[keyof StorageKeysTypes],
  data: T
): Promise<APIResponse<T>> => {
  try {
    const jsonData = JSON.stringify(data);
    await AsyncStorage.setItem(key, jsonData);
    return {
      success: true,
      data,
      message: "Data saved successfully",
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error saving to storage:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Generic function to load data from AsyncStorage
 */
export const loadFromStorage = async <T>(
  key: StorageKeysTypes[keyof StorageKeysTypes],
  defaultValue: T
): Promise<APIResponse<T>> => {
  try {
    const jsonData = await AsyncStorage.getItem(key);
    if (jsonData === null) {
      return {
        success: true,
        data: defaultValue,
        message: "No data found, returning default value",
        timestamp: new Date().toISOString(),
      };
    }

    const data = JSON.parse(jsonData) as T;
    return {
      success: true,
      data,
      message: "Data loaded successfully",
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error loading from storage:", error);
    return {
      success: false,
      data: defaultValue,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Generic function to remove data from AsyncStorage
 */
export const removeFromStorage = async (
  key: StorageKeysTypes[keyof StorageKeysTypes]
): Promise<APIResponse<boolean>> => {
  try {
    await AsyncStorage.removeItem(key);
    return {
      success: true,
      data: true,
      message: "Data removed successfully",
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error removing from storage:", error);
    return {
      success: false,
      data: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

// ============================================================================
// Item Storage Functions
// ============================================================================

/**
 * Save all stories to storage
 */
export const saveStories = async (stories: Item[]): Promise<APIResponse<Item[]>> => {
  return saveToStorage(STORAGE_KEYS.STORIES, stories);
};

/**
 * Load all stories from storage
 */
export const loadStories = async (): Promise<APIResponse<Item[]>> => {
  return loadFromStorage(STORAGE_KEYS.STORIES, []);
};

/**
 * Add a new item or update existing one
 */
export const saveItem = async (item: Item): Promise<APIResponse<Item>> => {
  try {
    const storiesResponse = await loadStories();
    if (!storiesResponse.success) {
      return {
        success: false,
        error: "Failed to load existing stories",
        timestamp: new Date().toISOString(),
      };
    }

    const stories = storiesResponse.data || [];
    const existingIndex = stories.findIndex(s => s.id === item.id);

    if (existingIndex >= 0) {
      // Update existing item
      stories[existingIndex] = { ...item, updatedAt: new Date().getTime() };
    } else {
      // Add new item
      stories.push({ ...item, createdAt: new Date().getTime(), updatedAt: new Date().getTime() });
    }

    const saveResponse = await saveStories(stories);
    if (saveResponse.success) {
      return {
        success: true,
        data: item,
        message: existingIndex >= 0 ? "Item updated successfully" : "Item created successfully",
        timestamp: new Date().toISOString(),
      };
    }
    return {
      success: false,
      error: saveResponse.error || "Failed to save item",
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error saving item:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Get a item by ID
 */
export const getItemById = async (id: string): Promise<APIResponse<Item | null>> => {
  try {
    const storiesResponse = await loadStories();
    if (!storiesResponse.success) {
      return {
        success: false,
        error: "Failed to load stories",
        timestamp: new Date().toISOString(),
      };
    }

    const stories = storiesResponse.data || [];
    const item = stories.find(s => s.id === id) || null;

    return {
      success: true,
      data: item,
      message: item ? "Item found" : "Item not found",
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error getting item by ID:", error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Delete a item by ID
 */
export const deleteItem = async (id: string): Promise<APIResponse<boolean>> => {
  try {
    const storiesResponse = await loadStories();
    if (!storiesResponse.success) {
      return {
        success: false,
        data: false,
        error: "Failed to load stories",
        timestamp: new Date().toISOString(),
      };
    }

    const stories = storiesResponse.data || [];
    const filteredStories = stories.filter(s => s.id !== id);

    if (filteredStories.length === stories.length) {
      return {
        success: false,
        data: false,
        error: "Item not found",
        timestamp: new Date().toISOString(),
      };
    }

    const saveResponse = await saveStories(filteredStories);
    return {
      success: saveResponse.success,
      data: saveResponse.success,
      message: saveResponse.success ? "Item deleted successfully" : "Failed to delete item",
      error: saveResponse.error,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error deleting item:", error);
    return {
      success: false,
      data: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

// ============================================================================
// Draft Storage Functions
// ============================================================================

/**
 * Save all drafts to storage
 */
export const saveDrafts = async (drafts: Item[]): Promise<APIResponse<Item[]>> => {
  return saveToStorage(STORAGE_KEYS.DRAFTS, drafts);
};

/**
 * Load all drafts from storage
 */
export const loadDrafts = async (): Promise<APIResponse<Item[]>> => {
  return loadFromStorage(STORAGE_KEYS.DRAFTS, []);
};

/**
 * Save a draft
 */
export const saveDraft = async (draft: Item): Promise<APIResponse<Item>> => {
  try {
    const draftsResponse = await loadDrafts();
    if (!draftsResponse.success) {
      return {
        success: false,
        error: "Failed to load existing drafts",
        timestamp: new Date().toISOString(),
      };
    }

    const drafts = draftsResponse.data || [];
    const existingIndex = drafts.findIndex(d => d.id === draft.id);

    const updatedDraft = { ...draft, lastSavedAt: new Date().toISOString() };

    if (existingIndex >= 0) {
      drafts[existingIndex] = updatedDraft;
    } else {
      drafts.push(updatedDraft);
    }

    const saveResponse = await saveDrafts(drafts);
    if (saveResponse.success) {
      return {
        success: true,
        data: updatedDraft,
        message: "Draft saved successfully",
        timestamp: new Date().toISOString(),
      };
    }
    return {
      success: false,
      error: saveResponse.error || "Failed to save draft",
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error saving draft:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Get a draft by ID
 */
export const getDraftById = async (id: string): Promise<APIResponse<Item | null>> => {
  try {
    const draftsResponse = await loadDrafts();
    if (!draftsResponse.success) {
      return {
        success: false,
        error: "Failed to load drafts",
        timestamp: new Date().toISOString(),
      };
    }

    const drafts = draftsResponse.data || [];
    const draft = drafts.find(d => d.id === id) || null;

    return {
      success: true,
      data: draft,
      message: draft ? "Draft found" : "Draft not found",
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error getting draft by ID:", error);
    return {
      success: false,
      data: null,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Delete a draft by ID
 */
export const deleteDraft = async (id: string): Promise<APIResponse<boolean>> => {
  try {
    const draftsResponse = await loadDrafts();
    if (!draftsResponse.success) {
      return {
        success: false,
        data: false,
        error: "Failed to load drafts",
        timestamp: new Date().toISOString(),
      };
    }

    const drafts = draftsResponse.data || [];
    const filteredDrafts = drafts.filter(d => d.id !== id);

    if (filteredDrafts.length === drafts.length) {
      return {
        success: false,
        data: false,
        error: "Draft not found",
        timestamp: new Date().toISOString(),
      };
    }

    const saveResponse = await saveDrafts(filteredDrafts);
    return {
      success: saveResponse.success,
      data: saveResponse.success,
      message: saveResponse.success ? "Draft deleted successfully" : "Failed to delete draft",
      error: saveResponse.error,
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error deleting draft:", error);
    return {
      success: false,
      data: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

// ============================================================================
// Utility Functions
// ============================================================================

/**
 * Generate a unique ID for new stories/drafts
 */
export const generateId = (): string => {
  return `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
};

/**
 * Clear all storage data (for testing/reset purposes)
 */
export const clearAllStorage = async (): Promise<APIResponse<boolean>> => {
  try {
    const keys = Object.values(STORAGE_KEYS);
    await Promise.all(keys.map(key => AsyncStorage.removeItem(key)));
    return {
      success: true,
      data: true,
      message: "All storage data cleared successfully",
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error clearing storage:", error);
    return {
      success: false,
      data: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Convert a item draft to a published item
 */
export const publishDraft = async (draftId: string): Promise<APIResponse<Item>> => {
  try {
    const draftResponse = await getDraftById(draftId);
    if (!draftResponse.success || !draftResponse.data) {
      return {
        success: false,
        error: "Draft not found",
        timestamp: new Date().toISOString(),
      };
    }

    const draft = draftResponse.data;
    const item: Item = {
      id: draft.id,
      name: draft.name,
      content: draft.content,
      images: draft.images,
      tags: draft.tags,
      currentLocation: draft.currentLocation,
      timeOfPossession: draft.timeOfPossession,
      createdAt: new Date().getTime(),
      updatedAt: new Date().getTime(),
    };

    // Save as published item
    const saveItemResponse = await saveItem(item);
    if (!saveItemResponse.success) {
      return saveItemResponse;
    }

    // Remove from drafts
    await deleteDraft(draftId);

    return {
      success: true,
      data: item,
      message: "Draft published successfully",
      timestamp: new Date().toISOString(),
    };
  } catch (error) {
    console.error("Error publishing draft:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};

/**
 * Auto-save functionality for drafts
 */
export const autoSaveDraft = async (draft: Item & { id: string }): Promise<APIResponse<Item>> => {
  try {
    return await saveDraft(draft);
  } catch (error) {
    console.error("Error auto-saving draft:", error);
    return {
      success: false,
      error: error instanceof Error ? error.message : "Unknown error occurred",
      timestamp: new Date().toISOString(),
    };
  }
};
