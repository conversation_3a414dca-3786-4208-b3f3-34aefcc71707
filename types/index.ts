/**
 * Global TypeScript interfaces for the Item Memory & Exchange Platform
 * Based on existing data structures and patterns in the codebase
 */

// ============================================================================
// User Related Types
// ============================================================================

export interface User {
  id: string;
  name: string;
  avatarUrl: string;
  // email?: string;
  /** 个人简介 */
  bio?: string;
  joinedAt?: string;
}

// ============================================================================
// Item Related Types
// ============================================================================

export interface TimelineItem {
  title: string;
  content: string;
  images?: string[];
  hasAudio?: boolean;
  date?: string;
  // type?: 'item' | 'memory' | 'event';
}

export interface Item {
  id?: string;
  title?: string;
  content?: string;
  coverImage?: string;
  /** 故事内容图片 */
  images?: string[];
  tags?: string[];
  /** 故事内容模式 */
  mode?: "normal" | "timeline";
  timelineItems?: TimelineItem[];
  createdAt?: number;
  updatedAt?: number;
}

// ============================================================================
// Form and Validation Types
// ============================================================================

export interface FormErrors {
  title?: string;
  content?: string;
  coverImage?: string;
  tags?: string;
  timelineItems?: string;
  [key: string]: string | undefined;
}

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | undefined;
}

export interface FormField {
  name: string;
  label: string;
  type: "text" | "textarea" | "image" | "tags" | "timeline";
  rules?: ValidationRule[];
  placeholder?: string;
  defaultValue?: any;
}

// ============================================================================
// Storage and API Types
// ============================================================================

export interface StorageKeys {
  STORIES: "stories";
  DRAFTS: "story_drafts";
  USER_PREFERENCES: "user_preferences";
  ITEMS: "items";
  POSTS: "posts";
  CATEGORIES: "categories";
  AI_ENHANCEMENTS: "ai_enhancements";
}

export interface APIResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
  timestamp?: string;
}

// ============================================================================
// App State and Navigation Types
// ============================================================================

// export interface AppState {
//   user?: User;
//   stories: Item[];
//   items: Item[];
//   posts: Post[];
//   isLoading: boolean;
//   error?: string;
// }

// ============================================================================
// Utility Types
// ============================================================================

export type ContentMode = "normal" | "timeline";
export type StoryStatus = "draft" | "published" | "archived";
export type ItemCondition = "excellent" | "good" | "fair" | "poor";

// ============================================================================
// Export all types for easy importing
// ============================================================================

// Type guards for runtime type checking
export const isStory = (obj: any): obj is Item => {
  return obj && typeof obj.id === "string" && typeof obj.title === "string";
};

export const isStoryDraft = (obj: any): obj is Item => {
  return obj && typeof obj.id === "string" && typeof obj.lastSavedAt === "string";
};

export const isTimelineItem = (obj: any): obj is TimelineItem => {
  return obj && typeof obj.id === "string" && typeof obj.title === "string";
};
