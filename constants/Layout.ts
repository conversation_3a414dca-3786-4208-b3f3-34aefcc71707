/**
 * Layout and spacing system for the Item Memory & Exchange Platform
 * Based on the prototype analysis with consistent 16px base spacing
 */

export const Layout = {
  // Spacing scale (in pixels)
  spacing: {
    xs: 4,
    sm: 8,
    md: 12,
    base: 16,
    lg: 20,
    xl: 24,
    '2xl': 32,
    '3xl': 48,
    '4xl': 64,
  },
  
  // Border radius scale
  borderRadius: {
    none: 0,
    sm: 4,
    md: 8,
    lg: 12,
    xl: 16,
    '2xl': 20,
    full: 9999,
  },
  
  // Common dimensions
  dimensions: {
    // Tab bar
    tabBarHeight: 80,
    tabBarPaddingBottom: 20,
    
    // Status bar (iOS simulation)
    statusBarHeight: 44,
    
    // Phone container (from prototypes)
    phoneWidth: 390,
    phoneHeight: 844,
    
    // Common element sizes
    buttonHeight: 48,
    buttonHeightSmall: 36,
    inputHeight: 48,
    avatarSize: 40,
    avatarSizeLarge: 80,
    iconSize: 24,
    iconSizeSmall: 16,
    iconSizeLarge: 32,
    
    // Card dimensions
    itemCardImageHeight: 160,
    thumbnailSize: 128,
    
    // Touch targets
    minTouchTarget: 44,
  },
  
  // Shadow styles
  shadows: {
    sm: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 1 },
      shadowOpacity: 0.05,
      shadowRadius: 2,
      elevation: 1,
    },
    md: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 2 },
      shadowOpacity: 0.08,
      shadowRadius: 4,
      elevation: 2,
    },
    lg: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 4 },
      shadowOpacity: 0.1,
      shadowRadius: 8,
      elevation: 4,
    },
    xl: {
      shadowColor: '#000',
      shadowOffset: { width: 0, height: 10 },
      shadowOpacity: 0.1,
      shadowRadius: 15,
      elevation: 8,
    },
  },
  
  // Grid system
  grid: {
    columns: {
      2: '50%',
      3: '33.333%',
      4: '25%',
    },
    gap: {
      sm: 8,
      md: 12,
      lg: 16,
    },
  },
  
  // Common layout patterns
  container: {
    padding: 16,
    paddingTop: 48, // Account for status bar
  },
  
  section: {
    marginBottom: 24,
  },
};
