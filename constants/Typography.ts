/**
 * Typography system for the Item Memory & Exchange Platform
 * Based on the prototype analysis using Apple system fonts
 */

export const Typography = {
  // Font families
  fontFamily: {
    primary: '-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif',
  },
  
  // Font sizes (in pixels, will be converted to appropriate units)
  fontSize: {
    xs: 12,
    sm: 14,
    base: 16,
    lg: 18,
    xl: 20,
    '2xl': 24,
    '3xl': 30,
    '4xl': 36,
  },
  
  // Font weights
  fontWeight: {
    normal: '400' as const,
    medium: '500' as const,
    semibold: '600' as const,
    bold: '700' as const,
  },
  
  // Line heights
  lineHeight: {
    tight: 1.2,
    normal: 1.4,
    relaxed: 1.6,
  },
  
  // Text styles for common use cases
  textStyles: {
    // Headers
    h1: {
      fontSize: 24,
      fontWeight: '700' as const,
      lineHeight: 1.2,
    },
    h2: {
      fontSize: 20,
      fontWeight: '600' as const,
      lineHeight: 1.3,
    },
    h3: {
      fontSize: 18,
      fontWeight: '600' as const,
      lineHeight: 1.3,
    },
    
    // Body text
    body: {
      fontSize: 16,
      fontWeight: '400' as const,
      lineHeight: 1.4,
    },
    bodySmall: {
      fontSize: 14,
      fontWeight: '400' as const,
      lineHeight: 1.4,
    },
    
    // UI elements
    button: {
      fontSize: 16,
      fontWeight: '500' as const,
      lineHeight: 1.2,
    },
    buttonSmall: {
      fontSize: 14,
      fontWeight: '500' as const,
      lineHeight: 1.2,
    },
    caption: {
      fontSize: 12,
      fontWeight: '400' as const,
      lineHeight: 1.3,
    },
    label: {
      fontSize: 14,
      fontWeight: '500' as const,
      lineHeight: 1.3,
    },
    
    // Tab bar
    tabLabel: {
      fontSize: 10,
      fontWeight: '400' as const,
      lineHeight: 1.2,
    },
  },
};
