{"expo": {"name": "Fr", "slug": "Fr", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "fr", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "This app needs access to camera to take photos for your stories.", "NSPhotoLibraryUsageDescription": "This app needs access to photo library to select images for your stories."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your stories.", "cameraPermission": "The app accesses your camera to let you take photos for your stories."}]], "experiments": {"typedRoutes": true}}}