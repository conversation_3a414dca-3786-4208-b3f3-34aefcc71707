import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { Pressable, TextInput, View, ViewStyle } from "react-native";
import Button from "./Button";


export interface TimelineItemData {
  id: string;
  title: string;
  content: string;
  hasImage?: boolean;
  hasAudio?: boolean;
}

export interface TimelineItemProps {
  item: TimelineItemData;
  isLast?: boolean;
  onEdit?: (item: TimelineItemData) => void;
  onAddImage?: (itemId: string) => void;
  onAddAudio?: (itemId: string) => void;
  onRemove?: (itemId: string) => void;
  style?: ViewStyle;
}

export function TimelineItem({ item, isLast = false, onEdit, onAddImage, onAddAudio, onRemove, style }: TimelineItemProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  return (
    <View style={[styles.container, style]}>
      {/* Timeline Line */}
      <View style={styles.timelineColumn}>
        <View style={[styles.timelineDot, { backgroundColor: colors.primary }]} />
        {!isLast && <View style={[styles.timelineLine, { backgroundColor: colors.borderLight }]} />}
      </View>

      {/* Content */}
      <View style={styles.contentColumn}>
        <View style={[styles.contentCard, { backgroundColor: colors.background }]}>
          <View style={styles.cardHeader}>
            <TextInput style={[styles.titleInput, { color: colors.text }]} value={item.title} placeholder="时间点标题" placeholderTextColor={colors.textMuted} onChangeText={text => onEdit?.({ ...item, title: text })} />
            <Pressable onPress={() => onRemove?.(item.id)} style={styles.menuButton}>
              <Ionicons name="ellipsis-vertical" size={16} color={colors.textMuted} />
            </Pressable>
          </View>

          <TextInput style={[styles.contentInput, { color: colors.textSecondary }]} value={item.content} placeholder="描述这个时间点发生的事情..." placeholderTextColor={colors.textMuted} multiline numberOfLines={3} onChangeText={text => onEdit?.({ ...item, content: text })} />
        </View>

        {/* Action Buttons */}
        <View style={styles.actionButtons}>
          <Button title="添加图片" variant="outline" size="small" icon={<Ionicons name="image-outline" size={16} color={colors.primary} />} iconPosition="left" onPress={() => onAddImage?.(item.id)} style={styles.actionButton} textStyle={styles.actionButtonText} />
          <Button title="添加语音" variant="outline" size="small" icon={<Ionicons name="mic-outline" size={16} color={colors.primary} />} iconPosition="left" onPress={() => onAddAudio?.(item.id)} style={styles.actionButton} textStyle={styles.actionButtonText} />
        </View>
      </View>
    </View>
  );
}

const styles = StyleSheetCreate({
  container: {
    flexDirection: "row",
    marginBottom: Layout.spacing.base,
  },
  timelineColumn: {
    width: 24,
    alignItems: "center",
    marginRight: Layout.spacing.base,
  },
  timelineDot: {
    width: 12,
    height: 12,
    borderRadius: 6,
    marginTop: 4,
  },
  timelineLine: {
    width: 2,
    flex: 1,
    marginTop: 4,
  },
  contentColumn: {
    flex: 1,
  },
  contentCard: {
    borderRadius: 12,
    padding: 12,
    marginBottom: 8,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.05,
    shadowRadius: 4,
    elevation: 2,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  titleInput: {
    flex: 1,
    fontSize: 14,
    fontWeight: "500",
    padding: 0,
  },
  menuButton: {
    padding: 4,
  },
  contentInput: {
    fontSize: 12,
    lineHeight: 16,
    padding: 0,
    textAlignVertical: "top",
  },
  actionButtons: {
    flexDirection: "row",
    gap: 8,
  },
  actionButton: {
    flex: 1,
  },
  actionButtonText: {
    fontSize: 12,
  },
});
