import { Colors } from '@/constants/Colors';
import { Layout } from '@/constants/Layout';
import { Typography } from '@/constants/Typography';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { Text, TouchableOpacity, View, ViewStyle } from 'react-native';

export interface HeaderAction {
  icon: string;
  onPress: () => void;
  badge?: boolean;
}

export interface HeaderProps {
  title?: string;
  leftAction?: HeaderAction;
  rightActions?: HeaderAction[];
  variant?: 'default' | 'transparent' | 'overlay';
  style?: ViewStyle;
}

export default function Header({
  title,
  leftAction,
  rightActions = [],
  variant = 'default',
  style,
}: HeaderProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getHeaderStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      height: Layout.dimensions.statusBarHeight + 44, // Status bar + header
      paddingTop: Layout.dimensions.statusBarHeight,
      paddingHorizontal: Layout.spacing.base,
      flexDirection: 'row',
      alignItems: 'center',
      justifyContent: 'space-between',
    };

    switch (variant) {
      case 'transparent':
        baseStyle.backgroundColor = 'transparent';
        break;
      case 'overlay':
        baseStyle.position = 'absolute';
        baseStyle.top = 0;
        baseStyle.left = 0;
        baseStyle.right = 0;
        baseStyle.zIndex = 10;
        baseStyle.backgroundColor = 'transparent';
        break;
      default:
        baseStyle.backgroundColor = colors.background;
        baseStyle.borderBottomWidth = 1;
        baseStyle.borderBottomColor = colors.borderLight;
    }

    return baseStyle;
  };

  const getActionButtonStyle = (isOverlay: boolean = false) => ({
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: isOverlay ? 'rgba(0, 0, 0, 0.3)' : colors.backgroundTertiary,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
  });

  const getActionIconColor = (isOverlay: boolean = false) => {
    return isOverlay ? colors.textInverse : colors.textSecondary;
  };

  const renderActionButton = (action: HeaderAction, index: number) => {
    const isOverlay = variant === 'overlay';
    
    return (
      <TouchableOpacity
        key={index}
        style={[
          getActionButtonStyle(isOverlay),
          index > 0 && { marginLeft: Layout.spacing.sm }
        ]}
        onPress={action.onPress}
        activeOpacity={0.7}
      >
        <Ionicons
          name={action.icon as any}
          size={20}
          color={getActionIconColor(isOverlay)}
        />
        {action.badge && (
          <View style={styles.badge}>
            <View style={[styles.badgeInner, { backgroundColor: colors.primary }]} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={[getHeaderStyle(), style]}>
      {/* Left side */}
      <View style={styles.leftContainer}>
        {leftAction && renderActionButton(leftAction, 0)}
      </View>

      {/* Center */}
      {title && (
        <View style={styles.centerContainer}>
          <Text
            style={[
              styles.title,
              {
                color: variant === 'overlay' ? colors.textInverse : colors.text,
                fontFamily: Typography.fontFamily.primary,
              }
            ]}
            numberOfLines={1}
          >
            {title}
          </Text>
        </View>
      )}

      {/* Right side */}
      <View style={styles.rightContainer}>
        {rightActions.map((action, index) => renderActionButton(action, index))}
      </View>
    </View>
  );
}

const styles = {
  leftContainer: {
    flex: 1,
    flexDirection: 'row' as const,
    justifyContent: 'flex-start' as const,
  },
  centerContainer: {
    flex: 2,
    alignItems: 'center' as const,
  },
  rightContainer: {
    flex: 1,
    flexDirection: 'row' as const,
    justifyContent: 'flex-end' as const,
  },
  title: {
    fontSize: Typography.textStyles.h3.fontSize,
    fontWeight: Typography.textStyles.h3.fontWeight,
  },
  badge: {
    position: 'absolute' as const,
    top: 8,
    right: 8,
  },
  badgeInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
};