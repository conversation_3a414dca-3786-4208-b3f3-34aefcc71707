import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { useTopBarHeight } from "@/hooks/useTopBarHeight";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { BlurView } from "expo-blur";
import { Animated, StyleSheet, Text, TouchableOpacity, View, ViewStyle } from "react-native";

export interface HeaderAction {
  icon: keyof typeof Ionicons.glyphMap;
  onPress: () => void;
  badge?: boolean;
}

export interface BlurHeaderProps {
  title?: string;
  leftAction?: HeaderAction;
  rightActions?: HeaderAction[];
  scrollY?: Animated.Value;
  style?: ViewStyle;
}

export default function BlurHeader({ title, leftAction, rightActions = [], scrollY, style }: BlurHeaderProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const topBarHeight = useTopBarHeight();

  const headerOpacity = scrollY
    ? scrollY.interpolate({
        inputRange: [0, 50],
        outputRange: [0, 1],
        extrapolate: "clamp",
      })
    : new Animated.Value(1);

  const getActionButtonStyle = () => ({
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: colors.backgroundTertiary,
    alignItems: "center" as const,
    justifyContent: "center" as const,
    backdropFilter: "blur(10px)",
  });

  const renderActionButton = (action: HeaderAction, index: number) => {
    return (
      <TouchableOpacity key={index} style={[getActionButtonStyle(), index > 0 && { marginLeft: Layout.spacing.sm }]} onPress={action.onPress} activeOpacity={0.7}>
        <Ionicons name={action.icon} size={20} color={colors.text} />
        {action.badge && (
          <View style={styles.badge}>
            <View style={[styles.badgeInner, { backgroundColor: colors.primary }]} />
          </View>
        )}
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { height: topBarHeight }, style]}>
      {/* Blur Background */}
      <Animated.View style={[styles.blurContainer, { opacity: headerOpacity }]}>
        <BlurView intensity={80} tint={colorScheme === "dark" ? "dark" : "light"} style={styles.blurView} />
      </Animated.View>

      {/* Solid Background for non-scrolled state */}
      <Animated.View
        style={[
          styles.solidBackground,
          {
            backgroundColor: colors.backgroundApp,
            opacity: scrollY
              ? scrollY.interpolate({
                  inputRange: [0, 10],
                  outputRange: [1, 0],
                  extrapolate: "clamp",
                })
              : 0,
          },
        ]}
      />

      {/* Header Content */}
      <View style={styles.headerContent}>
        {/* Left side */}
        <View style={styles.leftContainer}>{leftAction && renderActionButton(leftAction, 0)}</View>

        {/* Center */}
        {title && (
          <View style={styles.centerContainer}>
            <Text
              style={[
                styles.title,
                {
                  color: colors.text,
                  fontFamily: Typography.fontFamily.primary,
                },
              ]}
              numberOfLines={1}
            >
              {title}
            </Text>
          </View>
        )}

        {/* Right side */}
        <View style={styles.rightContainer}>{rightActions.map((action, index) => renderActionButton(action, index))}</View>
      </View>

      {/* Bottom border */}
      <Animated.View
        style={[
          styles.bottomBorder,
          {
            backgroundColor: colors.borderLight,
            opacity: headerOpacity,
          },
        ]}
      />
    </View>
  );
}

const styles = StyleSheetCreate({
  container: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    zIndex: 1000,
  },
  blurContainer: {
    ...StyleSheet.absoluteFillObject,
  },
  blurView: {
    flex: 1,
  },
  solidBackground: {
    ...StyleSheet.absoluteFillObject,
  },
  headerContent: {
    flex: 1,
    paddingTop: Layout.dimensions.statusBarHeight,
    paddingHorizontal: Layout.spacing.base,
    flexDirection: "row" as const,
    alignItems: "center" as const,
    justifyContent: "space-between" as const,
  },
  leftContainer: {
    flex: 1,
    flexDirection: "row" as const,
    justifyContent: "flex-start" as const,
  },
  centerContainer: {
    flex: 2,
    alignItems: "center" as const,
  },
  rightContainer: {
    flex: 1,
    flexDirection: "row" as const,
    justifyContent: "flex-end" as const,
  },
  title: {
    fontSize: Typography.textStyles.h3.fontSize,
    fontWeight: Typography.textStyles.h3.fontWeight,
  },
  badge: {
    position: "absolute" as const,
    top: 8,
    right: 8,
  },
  badgeInner: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  bottomBorder: {
    position: "absolute" as const,
    bottom: 0,
    left: 0,
    right: 0,
    height: 1,
  },
});
