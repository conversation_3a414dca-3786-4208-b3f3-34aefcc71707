import { Colors } from '@/constants/Colors';
import { Layout } from '@/constants/Layout';
import { Typography } from '@/constants/Typography';
import { useColorScheme } from '@/hooks/useColorScheme';
import { Ionicons } from '@expo/vector-icons';
import { Text, TouchableOpacity, View } from 'react-native';

export interface TabItem {
  key: string;
  label: string;
  icon: string;
  onPress: () => void;
}

export interface TabBarProps {
  tabs: TabItem[];
  activeTab: string;
  onAddPress?: () => void;
  style?: any;
}

export default function TabBar({ tabs, activeTab, onAddPress, style }: TabBarProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const renderTab = (tab: TabItem, index: number) => {
    const isActive = tab.key === activeTab;
    const isMiddle = index === Math.floor(tabs.length / 2);

    // Special handling for the middle tab (add button)
    if (isMiddle && onAddPress) {
      return (
        <TouchableOpacity
          key={tab.key}
          style={styles.addButton}
          onPress={onAddPress}
          activeOpacity={0.7}
        >
          <View style={[styles.addButtonInner, { backgroundColor: colors.primary }]}>
            <Ionicons
              name="add"
              size={24}
              color={colors.textInverse}
            />
          </View>
        </TouchableOpacity>
      );
    }

    return (
      <TouchableOpacity
        key={tab.key}
        style={styles.tabItem}
        onPress={tab.onPress}
        activeOpacity={0.7}
      >
        <Ionicons
          name={tab.icon as any}
          size={24}
          color={isActive ? colors.tabIconSelected : colors.tabIconDefault}
        />
        <Text
          style={[
            styles.tabLabel,
            {
              color: isActive ? colors.tabIconSelected : colors.tabIconDefault,
              fontFamily: Typography.fontFamily.primary,
            }
          ]}
        >
          {tab.label}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, { backgroundColor: colors.tabBackground }, style]}>
      <View style={styles.tabsContainer}>
        {tabs.map((tab, index) => renderTab(tab, index))}
      </View>
    </View>
  );
}

const styles = {
  container: {
    position: 'absolute' as const,
    bottom: 0,
    left: 0,
    right: 0,
    height: Layout.dimensions.tabBarHeight,
    borderTopWidth: 1,
    borderTopColor: Colors.light.border,
    paddingBottom: Layout.dimensions.tabBarPaddingBottom,
  },
  tabsContainer: {
    flex: 1,
    flexDirection: 'row' as const,
    justifyContent: 'space-around' as const,
    alignItems: 'center' as const,
  },
  tabItem: {
    flex: 1,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    paddingVertical: Layout.spacing.sm,
  },
  tabLabel: {
    fontSize: Typography.textStyles.tabLabel.fontSize,
    fontWeight: Typography.textStyles.tabLabel.fontWeight,
    marginTop: Layout.spacing.xs,
  },
  addButton: {
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    marginTop: -20,
  },
  addButtonInner: {
    width: 56,
    height: 56,
    borderRadius: 28,
    alignItems: 'center' as const,
    justifyContent: 'center' as const,
    ...Layout.shadows.md,
  },
};
