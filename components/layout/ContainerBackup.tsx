import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { BottomTabBarHeightContext } from "@react-navigation/bottom-tabs";
import { useContext } from "react";
import { ScrollView, View, ViewStyle } from "react-native";

export interface ContainerProps {
  children: React.ReactNode;
  scrollable?: boolean;
  padding?: "none" | "small" | "medium" | "large";
  backgroundColor?: string;
  style?: ViewStyle;
  contentContainerStyle?: ViewStyle;
  showsVerticalScrollIndicator?: boolean;
  hasTabBar?: boolean; // Whether to account for tab bar height
}

export default function ContainerBackup({ children, scrollable = false, padding = "medium", backgroundColor, style, contentContainerStyle, showsVerticalScrollIndicator = false, hasTabBar = true }: ContainerProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const height = useContext(BottomTabBarHeightContext);

  let tabBarHeight = Layout.dimensions.tabBarHeight;
  if (height !== undefined) {
    tabBarHeight = height;
  }

  const getContainerStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      flex: 1,
      backgroundColor: backgroundColor || colors.backgroundSecondary,
    };

    return baseStyle;
  };

  const getContentStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {};

    switch (padding) {
      case "none":
        break;
      case "small":
        baseStyle.padding = Layout.spacing.sm;
        break;
      case "large":
        baseStyle.padding = Layout.spacing.xl;
        break;
      default:
        baseStyle.padding = Layout.spacing.base;
    }

    // Add top padding for status bar if not scrollable
    if (!scrollable) {
      baseStyle.paddingTop = Layout.container.paddingTop;
    }

    // For scrollable content, add bottom padding to ensure content can scroll above tab bar
    // For non-scrollable content, add bottom margin to prevent overlap
    if (hasTabBar && scrollable) {
      baseStyle.paddingBottom = tabBarHeight || Layout.dimensions.tabBarHeight;
    } else if (hasTabBar && !scrollable) {
      baseStyle.marginBottom = tabBarHeight || Layout.dimensions.tabBarHeight;
    }

    return baseStyle;
  };

  if (scrollable) {
    return (
      <ScrollView style={[getContainerStyle(), style]} contentContainerStyle={[getContentStyle(), contentContainerStyle]} showsVerticalScrollIndicator={showsVerticalScrollIndicator}>
        {children}
      </ScrollView>
    );
  }

  return <View style={[getContainerStyle(), getContentStyle(), style]}>{children}</View>;
}
