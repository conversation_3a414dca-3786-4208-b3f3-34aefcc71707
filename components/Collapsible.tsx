import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { PropsWithChildren, useState } from "react";
import { TouchableOpacity } from "react-native";
import { ThemedText, ThemedView } from "./index";

export default function Collapsible({ children, title }: PropsWithChildren & { title: string }) {
  const [isOpen, setIsOpen] = useState(false);
  const theme = useColorScheme() ?? "light";

  return (
    <ThemedView>
      <TouchableOpacity style={styles.heading} onPress={() => setIsOpen(value => !value)} activeOpacity={0.8}>
        <Ionicons name="chevron-forward" size={18} color={theme === "light" ? Colors.light.icon : Colors.dark.icon} style={{ transform: [{ rotate: isOpen ? "90deg" : "0deg" }] }} />

        <ThemedText type="defaultSemiBold">{title}</ThemedText>
      </TouchableOpacity>
      {isOpen && <ThemedView style={styles.content}>{children}</ThemedView>}
    </ThemedView>
  );
}

const styles = StyleSheetCreate({
  heading: {
    flexDirection: "row",
    alignItems: "center",
    gap: 6,
  },
  content: {
    marginTop: 6,
    marginLeft: 24,
  },
});
