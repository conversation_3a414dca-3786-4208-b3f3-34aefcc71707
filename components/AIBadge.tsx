import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { ComponentProps } from "react";
import { Text, View } from "react-native";

export interface AIBadgeProps {
  text: string;
  icon?: ComponentProps<typeof Ionicons>['name'];
  variant?: "default" | "small" | "large";
  style?: any;
}

export function AIBadge({ text, icon = "hardware-chip", variant = "default", style }: AIBadgeProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const getSize = () => {
    switch (variant) {
      case "small":
        return { fontSize: 10, iconSize: 10, padding: 4 };
      case "large":
        return { fontSize: 14, iconSize: 14, padding: 8 };
      default:
        return { fontSize: 12, iconSize: 12, padding: 6 };
    }
  };

  const size = getSize();

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: colors.aiBadge,
          paddingHorizontal: size.padding,
          paddingVertical: size.padding / 2,
        },
        style,
      ]}
    >
      <Ionicons name={icon} size={size.iconSize} color={colors.aiText} />
      <Text
        style={[
          styles.text,
          {
            color: colors.aiText,
            fontSize: size.fontSize,
            marginLeft: icon ? 4 : 0,
          },
        ]}
      >
        {text}
      </Text>
    </View>
  );
}

const styles = StyleSheetCreate({
  container: {
    flexDirection: "row",
    alignItems: "center",
    borderRadius: 4,
    alignSelf: "flex-start",
  },
  text: {
    fontWeight: "500",
  },
});
