import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Pressable, Text, View, ViewStyle } from "react-native";

export interface StatCardProps {
  value: string | number;
  label: string;
  color?: string;
  onPress?: () => void;
  style?: ViewStyle;
}

export function StatCard({ value, label, color, onPress, style }: StatCardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const Component = onPress ? Pressable : View;

  return (
    <Component
      style={[
        styles.container,
        {
          backgroundColor: colors.background,
          shadowColor: colors.shadowCard,
        },
        style,
      ]}
      onPress={onPress}
    >
      <Text
        style={[
          styles.value,
          {
            color: color || colors.primary,
          },
        ]}
      >
        {value}
      </Text>
      <Text
        style={[
          styles.label,
          {
            color: colors.textMuted,
          },
        ]}
      >
        {label}
      </Text>
    </Component>
  );
}

const styles = StyleSheetCreate({
  container: {
    borderRadius: 16,
    padding: Layout.spacing.base,
    alignItems: "center",
    justifyContent: "center",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.05,
    shadowRadius: 12,
    elevation: 2,
    minHeight: 80,
  },
  value: {
    fontSize: 24,
    fontWeight: "bold",
    marginBottom: 4,
  },
  label: {
    fontSize: 12,
    textAlign: "center",
  },
});
