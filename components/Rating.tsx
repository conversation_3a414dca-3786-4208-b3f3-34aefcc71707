import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { TouchableOpacity, View, ViewStyle } from "react-native";

export interface RatingProps {
  rating: number;
  maxRating?: number;
  size?: "small" | "medium" | "large";
  interactive?: boolean;
  onRatingChange?: (rating: number) => void;
  style?: ViewStyle;
}

export default function Rating({ rating, maxRating = 5, size = "medium", interactive = false, onRatingChange, style }: RatingProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const getStarSize = () => {
    switch (size) {
      case "small":
        return 12;
      case "large":
        return 24;
      default:
        return 16;
    }
  };

  const handleStarPress = (starIndex: number) => {
    if (interactive && onRatingChange) {
      onRatingChange(starIndex + 1);
    }
  };

  const renderStar = (index: number) => {
    const isFilled = index < rating;
    const starSize = getStarSize();

    const StarComponent = interactive ? TouchableOpacity : View;

    return (
      <StarComponent key={index} onPress={() => handleStarPress(index)} style={[styles.star, { marginRight: index < maxRating - 1 ? 2 : 0 }]} disabled={!interactive}>
        <Ionicons name={isFilled ? "star" : "star-outline"} size={starSize} color={isFilled ? colors.primary : colors.textMuted} />
      </StarComponent>
    );
  };

  return <View style={[styles.container, style]}>{Array.from({ length: maxRating }, (_, index) => renderStar(index))}</View>;
}

const styles = StyleSheetCreate({
  container: {
    flexDirection: "row",
    alignItems: "center",
  },
  star: {
    alignItems: "center",
    justifyContent: "center",
  },
});
