import { Colors } from '@/constants/Colors';
import { Layout } from '@/constants/Layout';
import { Typography } from '@/constants/Typography';
import { useColorScheme } from '@/hooks/useColorScheme';
import { StyleSheetCreate } from "@/utils";
import React from 'react';
import { Text, TextStyle, View, ViewStyle } from 'react-native';

export interface BadgeProps {
  text: string;
  variant?: 'story' | '3d' | 'value' | 'category' | 'default';
  icon?: React.ReactNode;
  style?: ViewStyle;
  textStyle?: TextStyle;
}

export default function Badge({
  text,
  variant = 'default',
  icon,
  style,
  textStyle,
}: BadgeProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getBadgeStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: Layout.borderRadius.lg,
      paddingHorizontal: Layout.spacing.sm,
      paddingVertical: 2,
      flexDirection: 'row',
      alignItems: 'center',
      alignSelf: 'flex-start',
    };

    switch (variant) {
      case 'story':
        baseStyle.backgroundColor = `${colors.primary}E6`; // 90% opacity
        break;
      case '3d':
        baseStyle.backgroundColor = `${colors.badge3D}E6`; // 90% opacity
        break;
      case 'value':
        baseStyle.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        break;
      case 'category':
        baseStyle.backgroundColor = colors.backgroundTertiary;
        baseStyle.borderRadius = Layout.borderRadius.full;
        baseStyle.paddingHorizontal = Layout.spacing.base;
        baseStyle.paddingVertical = Layout.spacing.xs;
        break;
      default:
        baseStyle.backgroundColor = colors.primary;
    }

    return baseStyle;
  };

  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontFamily: Typography.fontFamily.primary,
      fontSize: Typography.fontSize.xs,
      fontWeight: Typography.fontWeight.normal,
    };

    switch (variant) {
      case 'story':
      case '3d':
      case 'value':
      case 'default':
        baseStyle.color = colors.textInverse;
        break;
      case 'category':
        baseStyle.color = colors.text;
        baseStyle.fontSize = Typography.fontSize.sm;
        baseStyle.fontWeight = Typography.fontWeight.medium;
        break;
    }

    return baseStyle;
  };

  return (
    <View style={[getBadgeStyle(), style]}>
      {icon && (
        <View style={{ marginRight: Layout.spacing.xs }}>
          {icon}
        </View>
      )}
      <Text style={[getTextStyle(), textStyle]}>{text}</Text>
    </View>
  );
}

const styles = StyleSheetCreate({
  // Additional styles if needed
});
