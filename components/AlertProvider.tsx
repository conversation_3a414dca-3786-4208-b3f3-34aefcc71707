import { getAlertState, registerAlertCallback } from "@/utils/alert";
import React, { useEffect, useState } from "react";
import Alert, { AlertButton } from "./Alert";

interface AlertState {
  visible: boolean;
  title?: string;
  message?: string;
  buttons?: AlertButton[];
  icon?: string;
  iconColor?: string;
  onClose: () => void;
}

export default function AlertProvider({ children }: { children: React.ReactNode }) {
  const [alertState, setAlertState] = useState<AlertState>(getAlertState());

  useEffect(() => {
    // Register the callback to update alert state
    registerAlertCallback((newState) => {
      setAlertState({ ...newState });
    });
  }, []);

  return (
    <>
      {children}
      <Alert
        visible={alertState.visible}
        title={alertState.title}
        message={alertState.message}
        buttons={alertState.buttons}
        onClose={alertState.onClose}
        icon={alertState.icon as any}
        iconColor={alertState.iconColor}
      />
    </>
  );
}
