import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { Text, View, ViewStyle } from "react-native";
import Button from "./Button";

export interface AIAssistantCardProps {
  type: "suggestion" | "assistant";
  title?: string;
  message: string;
  actions?: Array<{
    title: string;
    icon?: string;
    onPress: () => void;
    variant?: "primary" | "outline";
  }>;
  style?: ViewStyle;
}

export function AIAssistantCard({ type, title, message, actions = [], style }: AIAssistantCardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  if (type === "suggestion") {
    return (
      <View style={[styles.suggestionContainer, { backgroundColor: colors.aiBadge }, style]}>
        <View style={styles.suggestionHeader}>
          <View style={[styles.aiIcon, { backgroundColor: colors.primary }]}>
            <Ionicons name="sparkles" size={18} color="white" />
          </View>
          <View style={styles.suggestionContent}>
            <View style={styles.suggestionTitleRow}>
              <Text style={[styles.suggestionTitle, { color: colors.text }]}>AI建议</Text>
              <View style={[styles.aiBadge, { backgroundColor: colors.primary }]}>
                <Text style={styles.aiBadgeText}>智能</Text>
              </View>
            </View>
            <Text style={[styles.suggestionText, { color: colors.textSecondary }]}>{message}</Text>
            {actions.length > 0 && (
              <View style={styles.suggestionActions}>
                {actions.map((action, index) => (
                  <Button key={index} title={action.title} variant={action.variant || "primary"} size="small" onPress={action.onPress} style={[styles.suggestionButton, index > 0 && styles.suggestionButtonSpacing]} textStyle={styles.suggestionButtonText} icon={action.icon ? <Ionicons name={action.icon as any} size={14} color={action.variant === "outline" ? colors.primary : "white"} /> : undefined} />
                ))}
              </View>
            )}
          </View>
        </View>
      </View>
    );
  }

  return (
    <View style={[styles.assistantContainer, { backgroundColor: colors.aiBadge }, style]}>
      <View style={styles.assistantHeader}>
        <View style={[styles.assistantAvatar, { backgroundColor: colors.primary }]}>
          <Ionicons name="hardware-chip" size={24} color="white" />
        </View>
        <View style={styles.assistantInfo}>
          <View style={styles.assistantTitleRow}>
            <Text style={[styles.assistantTitle, { color: colors.text }]}>{title || "AI创作助手"}</Text>
            <View style={[styles.aiStatusBadge, { backgroundColor: "#10B981" }]}>
              <View style={styles.aiStatusDot} />
              <Text style={styles.aiStatusText}>在线</Text>
            </View>
          </View>
          <Text style={[styles.assistantSubtitle, { color: colors.textMuted }]}>让AI帮你完善故事细节</Text>
        </View>
      </View>

      {message && (
        <View style={[styles.assistantMessageContainer, { backgroundColor: colors.background }]}>
          <Text style={[styles.assistantMessage, { color: colors.textSecondary }]}>{message}</Text>
        </View>
      )}

      {actions.length > 0 && (
        <View style={styles.assistantActions}>
          {actions.map((action, index) => (
            <Button key={index} title={action.title} variant={action.variant || "primary"} size="small" icon={action.icon ? <Ionicons name={action.icon as any} size={16} color={action.variant === "outline" ? colors.primary : "white"} /> : undefined} iconPosition="left" onPress={action.onPress} style={styles.assistantActionButton} textStyle={styles.assistantActionText} />
          ))}
        </View>
      )}
    </View>
  );
}

const styles = StyleSheetCreate({
  suggestionContainer: {
    borderRadius: 16,
    padding: 16,
    marginBottom: Layout.spacing.base,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.08,
    shadowRadius: 12,
    elevation: 4,
  },
  suggestionHeader: {
    flexDirection: "row",
    alignItems: "flex-start",
  },
  aiIcon: {
    width: 36,
    height: 36,
    borderRadius: 18,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  suggestionContent: {
    flex: 1,
  },
  suggestionTitleRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 6,
  },
  suggestionTitle: {
    fontSize: 16,
    fontWeight: "600",
  },
  aiBadge: {
    paddingHorizontal: 8,
    paddingVertical: 2,
    borderRadius: 12,
  },
  aiBadgeText: {
    color: "white",
    fontSize: 10,
    fontWeight: "600",
  },
  suggestionText: {
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 12,
  },
  suggestionActions: {
    flexDirection: "row",
    gap: 8,
    flexWrap: "wrap",
  },
  suggestionButton: {
    paddingHorizontal: 16,
    minWidth: 80,
  },
  suggestionButtonSpacing: {
    marginLeft: 8,
  },
  suggestionButtonText: {
    fontSize: 12,
    fontWeight: "500",
  },
  assistantContainer: {
    borderRadius: 20,
    padding: 20,
    marginBottom: Layout.spacing.base,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 16,
    elevation: 6,
  },
  assistantHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
  },
  assistantAvatar: {
    width: 48,
    height: 48,
    borderRadius: 24,
    justifyContent: "center",
    alignItems: "center",
    marginRight: 16,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.15,
    shadowRadius: 6,
    elevation: 3,
  },
  assistantInfo: {
    flex: 1,
  },
  assistantTitleRow: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "space-between",
    marginBottom: 4,
  },
  assistantTitle: {
    fontSize: 18,
    fontWeight: "600",
  },
  aiStatusBadge: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    gap: 4,
  },
  aiStatusDot: {
    width: 6,
    height: 6,
    borderRadius: 3,
    backgroundColor: "white",
  },
  aiStatusText: {
    color: "white",
    fontSize: 10,
    fontWeight: "600",
  },
  assistantSubtitle: {
    fontSize: 13,
    lineHeight: 18,
  },
  assistantMessageContainer: {
    padding: 12,
    borderRadius: 12,
    marginBottom: 16,
  },
  assistantMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
  assistantActions: {
    flexDirection: "row",
    gap: 10,
    flexWrap: "wrap",
  },
  assistantActionButton: {
    flex: 1,
    minWidth: 120,
  },
  assistantActionText: {
    fontSize: 13,
    fontWeight: "500",
  },
});
