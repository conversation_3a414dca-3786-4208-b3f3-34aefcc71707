import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { useEffect } from "react";
import { Modal, Pressable, Text, View } from "react-native";
import { Gesture, GestureHandlerRootView } from "react-native-gesture-handler";
import { clamp, useAnimatedStyle, useSharedValue, withSpring } from "react-native-reanimated";

interface FullscreenImageModalProps {
  visible: boolean;
  images: string[];
  currentIndex: number;
  onClose: () => void;
  onIndexChange: (index: number) => void;
}

export default function FullscreenImageModal({ visible, images, currentIndex, onClose, onIndexChange }: FullscreenImageModalProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  // Zoom and pan state using Reanimated
  const scale = useSharedValue(1);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);

  const MIN_SCALE = 1;
  const MAX_SCALE = 3;

  // Reset zoom when image changes
  useEffect(() => {
    resetZoom();
  }, [currentIndex]);

  const resetZoom = () => {
    scale.value = withSpring(1);
    translateX.value = withSpring(0);
    translateY.value = withSpring(0);
  };

  // Pinch gesture for zoom
  const pinchGesture = Gesture.Pinch()
    .onUpdate(event => {
      const newScale = clamp(event.scale, MIN_SCALE, MAX_SCALE);
      scale.value = newScale;
    })
    .onEnd(() => {
      if (scale.value < MIN_SCALE) {
        scale.value = withSpring(MIN_SCALE);
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
      }
    });

  // Pan gesture for moving when zoomed
  const panGesture = Gesture.Pan()
    .onUpdate(event => {
      if (scale.value > 1) {
        translateX.value = event.translationX;
        translateY.value = event.translationY;
      }
    })
    .onEnd(() => {
      // Keep current position
    });

  // Combine gestures
  const composedGesture = Gesture.Simultaneous(pinchGesture, panGesture);

  const handlePrevious = () => {
    const newIndex = currentIndex > 0 ? currentIndex - 1 : images.length - 1;
    onIndexChange(newIndex);
  };

  const handleNext = () => {
    const newIndex = currentIndex < images.length - 1 ? currentIndex + 1 : 0;
    onIndexChange(newIndex);
  };

  // Animated style for the image
  const animatedStyle = useAnimatedStyle(() => {
    return {
      transform: [{ scale: scale.value }, { translateX: translateX.value }, { translateY: translateY.value }],
    };
  });

  // Double tap to reset zoom
  const handleDoubleTap = () => {
    if (scale.value > 1) {
      resetZoom();
    } else {
      // Zoom in to 2x on double tap
      scale.value = withSpring(2);
      translateX.value = withSpring(0);
      translateY.value = withSpring(0);
    }
  };

  const handleBackgroundPress = () => {
    onClose();
  };

  if (!visible || images.length === 0) {
    return null;
  }

  return (
    <GestureHandlerRootView>
      <Modal visible={visible} transparent animationType="fade" onRequestClose={onClose} statusBarTranslucent>
        <Pressable style={styles.fullscreenOverlay} onPress={handleBackgroundPress}>
          {/* Close Button */}
          <Pressable style={styles.fullscreenClose} onPress={onClose}>
            <Ionicons name="close" size={20} color="white" />
          </Pressable>

          {/* Navigation Buttons - Only show if more than 1 image */}
          {images.length > 1 && (
            <>
              <Pressable style={[styles.navButton, styles.navButtonLeft]} onPress={handlePrevious}>
                <Ionicons name="chevron-back" size={20} color="white" />
              </Pressable>

              <Pressable style={[styles.navButton, styles.navButtonRight]} onPress={handleNext}>
                <Ionicons name="chevron-forward" size={20} color="white" />
              </Pressable>
            </>
          )}

          {/* Image Container with Zoom Support */}
          {/* <View style={styles.imageContainer}>
            <GestureDetector gesture={composedGesture}>
              <Animated.View style={[styles.zoomImageContainer, animatedStyle]}>
                <Image source={{ uri: images[currentIndex] }} style={styles.fullscreenImage} contentFit="contain" />
              </Animated.View>
            </GestureDetector>
          </View> */}

          <Pressable
            style={styles.imageContainer}
            // onTouchStart={handleTouchStart}
            // onTouchEnd={handleTouchEnd}
            onPress={e => e.stopPropagation()}
          >
            <Image source={{ uri: images[currentIndex] }} style={styles.fullscreenImage} contentFit="contain" />
          </Pressable>

          {/* Image Counter - Only show if more than 1 image */}
          {images.length > 1 && (
            <View style={styles.fullscreenInfo}>
              <Text style={styles.fullscreenCounter}>
                {currentIndex + 1} / {images.length}
              </Text>
            </View>
          )}
        </Pressable>
      </Modal>
    </GestureHandlerRootView>
  );
}

const styles = StyleSheetCreate({
  fullscreenOverlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.95)",
    justifyContent: "center",
    alignItems: "center",
  },
  fullscreenClose: {
    position: "absolute",
    top: 60,
    right: 20,
    zIndex: 3,
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  navButton: {
    position: "absolute",
    top: "50%",
    transform: [{ translateY: -18 }],
    width: 36,
    height: 36,
    borderRadius: 18,
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    justifyContent: "center",
    alignItems: "center",
    zIndex: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  navButtonLeft: {
    left: 20,
  },
  navButtonRight: {
    right: 20,
  },
  imageContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  zoomImageContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    width: "100%",
  },
  fullscreenImage: {
    width: "90%",
    height: "80%",
    maxWidth: 400,
    maxHeight: 600,
  },
  fullscreenInfo: {
    position: "absolute",
    bottom: 60,
    alignSelf: "center",
    backgroundColor: "rgba(0, 0, 0, 0.6)",
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.lg,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 5,
  },
  fullscreenCounter: {
    color: "white",
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
  },
});
