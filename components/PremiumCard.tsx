import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { LinearGradient } from "expo-linear-gradient";
import { Text, View, ViewStyle } from "react-native";
import Button from "./Button";

export interface PremiumCardProps {
  title: string;
  subtitle: string;
  daysRemaining?: number;
  onUpgrade?: () => void;
  style?: ViewStyle;
}

export function PremiumCard({ title, subtitle, daysRemaining, onUpgrade, style }: PremiumCardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  return (
    <LinearGradient colors={[colors.primary, colors.secondary]} start={{ x: 0, y: 0 }} end={{ x: 1, y: 1 }} style={[styles.container, style]}>
      <View style={styles.header}>
        <View style={styles.titleContainer}>
          <Ionicons name="diamond" size={16} color="white" />
          <Text style={styles.title}>{title}</Text>
        </View>
        {daysRemaining && (
          <View style={styles.daysContainer}>
            <Text style={styles.daysText}>剩余{daysRemaining}天</Text>
          </View>
        )}
      </View>

      <Text style={styles.subtitle}>{subtitle}</Text>

      {onUpgrade && <Button title="续费会员" variant="secondary" size="small" onPress={onUpgrade} style={styles.upgradeButton} textStyle={styles.upgradeButtonText} />}
    </LinearGradient>
  );
}

const styles = StyleSheetCreate({
  container: {
    borderRadius: 16,
    padding: Layout.spacing.base,
    marginBottom: Layout.spacing.base,
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  titleContainer: {
    flexDirection: "row",
    alignItems: "center",
    gap: 8,
  },
  title: {
    color: "white",
    fontSize: 16,
    fontWeight: "500",
  },
  daysContainer: {
    backgroundColor: "rgba(255, 255, 255, 0.2)",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  daysText: {
    color: "white",
    fontSize: 12,
  },
  subtitle: {
    color: "rgba(255, 255, 255, 0.8)",
    fontSize: 12,
    marginBottom: 12,
    lineHeight: 16,
  },
  upgradeButton: {
    backgroundColor: "white",
    alignSelf: "flex-start",
  },
  upgradeButtonText: {
    color: "#5D8BF4",
    fontWeight: "600",
  },
});
