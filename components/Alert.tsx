import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { useEffect, useRef } from "react";
import { Animated, Modal, Pressable, Text, TouchableOpacity, View } from "react-native";

export interface AlertButton {
  text: string;
  onPress?: () => void;
  style?: "default" | "cancel" | "destructive";
}

export interface AlertProps {
  visible: boolean;
  title?: string;
  message?: string;
  buttons?: AlertButton[];
  onClose: () => void;
  icon?: keyof typeof Ionicons.glyphMap;
  iconColor?: string;
}

export default function Alert({ visible, title, message, buttons = [], onClose, icon, iconColor }: AlertProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];
  const scaleAnim = useRef(new Animated.Value(0)).current;
  const opacityAnim = useRef(new Animated.Value(0)).current;

  useEffect(() => {
    if (visible) {
      // Animate in
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 1,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(opacityAnim, {
          toValue: 1,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      // Animate out
      Animated.parallel([
        Animated.spring(scaleAnim, {
          toValue: 0,
          useNativeDriver: true,
          tension: 100,
          friction: 8,
        }),
        Animated.timing(opacityAnim, {
          toValue: 0,
          duration: 150,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [visible]);

  const handleBackdropPress = () => {
    // Only close if there's a cancel button or no buttons at all
    const hasCancelButton = buttons.some(button => button.style === "cancel");
    if (hasCancelButton || buttons.length === 0) {
      onClose();
    }
  };

  const handleButtonPress = (button: AlertButton) => {
    if (button.onPress) {
      button.onPress();
    }
    onClose();
  };

  const getButtonStyle = (buttonStyle: AlertButton["style"]) => {
    switch (buttonStyle) {
      case "destructive":
        return {
          backgroundColor: colors.error,
          color: colors.textInverse,
        };
      case "cancel":
        return {
          backgroundColor: colors.backgroundTertiary,
          color: colors.textSecondary,
        };
      default:
        return {
          backgroundColor: colors.primary,
          color: colors.textInverse,
        };
    }
  };

  const getIconColor = () => {
    if (iconColor) return iconColor;
    if (buttons.some(button => button.style === "destructive")) {
      return colors.error;
    }
    return colors.primary;
  };

  if (!visible) {
    return null;
  }

  return (
    <Modal visible={visible} transparent animationType="none" onRequestClose={onClose} statusBarTranslucent>
      <Animated.View style={[styles.overlay, { opacity: opacityAnim }]}>
        <Pressable style={styles.backdrop} onPress={handleBackdropPress}>
          <Animated.View
            style={[
              styles.alertContainer,
              {
                backgroundColor: colors.background,
                borderColor: colors.border,
                transform: [{ scale: scaleAnim }],
              },
            ]}
          >
            <Pressable onPress={e => e.stopPropagation()}>
              {/* Header with Icon and Title */}
              {(icon || title) && (
                <View style={styles.headerContainer}>
                  <View style={styles.headerContent}>
                    {icon && (
                      <View style={[styles.iconBackground, { backgroundColor: `${getIconColor()}15` }]}>
                        <Ionicons name={icon} size={24} color={getIconColor()} />
                      </View>
                    )}
                    {title && (
                      <Text style={[styles.title, { color: colors.text }]} numberOfLines={2}>
                        {title}
                      </Text>
                    )}
                  </View>
                </View>
              )}

              {/* Content */}
              <View style={styles.contentContainer}>
                {message && (
                  <Text style={[styles.message, { color: colors.textSecondary }]} numberOfLines={4}>
                    {message}
                  </Text>
                )}
              </View>

              {/* Buttons */}
              {buttons.length > 0 && (
                <View style={[styles.buttonContainer, buttons.length > 2 && styles.buttonContainerVertical]}>
                  {buttons.map((button, index) => {
                    const buttonStyles = getButtonStyle(button.style);
                    const isLastButton = index === buttons.length - 1;
                    const isSingleButton = buttons.length === 1;
                    const isVerticalLayout = buttons.length > 2;

                    return (
                      <TouchableOpacity
                        key={index}
                        style={[
                          styles.button,
                          isVerticalLayout && styles.buttonVertical,
                          {
                            backgroundColor: buttonStyles.backgroundColor,
                            marginRight: !isLastButton && !isSingleButton && !isVerticalLayout ? Layout.spacing.sm : 0,
                            marginBottom: isVerticalLayout && !isLastButton ? Layout.spacing.sm : 0,
                            flex: isSingleButton ? 1 : isVerticalLayout ? 0 : 1,
                          },
                        ]}
                        onPress={() => handleButtonPress(button)}
                        activeOpacity={0.7}
                      >
                        <Text style={[styles.buttonText, { color: buttonStyles.color }]}>{button.text}</Text>
                      </TouchableOpacity>
                    );
                  })}
                </View>
              )}
            </Pressable>
          </Animated.View>
        </Pressable>
      </Animated.View>
    </Modal>
  );
}

const styles = StyleSheetCreate({
  overlay: {
    flex: 1,
    backgroundColor: "rgba(0, 0, 0, 0.5)",
    justifyContent: "center",
    alignItems: "center",
  },
  backdrop: {
    flex: 1,
    width: "100%",
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.xl,
  },
  alertContainer: {
    width: "100%",
    maxWidth: 320,
    borderRadius: Layout.borderRadius.xl,
    borderWidth: 1,
    padding: Layout.spacing.xl,
    ...Layout.shadows.lg,
  },
  headerContainer: {
    width: "100%",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: Layout.spacing.lg,
  },
  headerContent: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
  },
  iconBackground: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    marginRight: Layout.spacing.sm,
  },
  contentContainer: {
    marginBottom: Layout.spacing.lg,
  },
  title: {
    fontSize: Typography.textStyles.h3.fontSize,
    fontWeight: Typography.textStyles.h3.fontWeight,
    lineHeight: Typography.textStyles.h3.lineHeight * Typography.textStyles.h3.fontSize,
    textAlign: "center",
    flexShrink: 1,
  },
  message: {
    fontSize: Typography.textStyles.body.fontSize,
    fontWeight: Typography.textStyles.body.fontWeight,
    lineHeight: Typography.textStyles.body.lineHeight * Typography.textStyles.body.fontSize,
    textAlign: "center",
  },
  buttonContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
  },
  buttonContainerVertical: {
    flexDirection: "column",
  },
  button: {
    height: Layout.dimensions.buttonHeight,
    borderRadius: Layout.borderRadius.lg,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: Layout.spacing.lg,
  },
  buttonVertical: {
    width: "100%",
  },
  buttonText: {
    fontSize: Typography.textStyles.button.fontSize,
    fontWeight: Typography.textStyles.button.fontWeight,
    textAlign: "center",
  },
});
