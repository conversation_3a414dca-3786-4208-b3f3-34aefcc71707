import { useThemeColor } from "@/hooks/useThemeColor";
import { StyleSheetCreate } from "@/utils";
import { Text, type TextProps } from "react-native";

export type ThemedTextProps = TextProps & {
  lightColor?: string;
  darkColor?: string;
  type?: "default" | "title" | "defaultSemiBold" | "subtitle" | "link";
};

export default function ThemedText({ style, lightColor, darkColor, type = "default", ...rest }: ThemedTextProps) {
  const color = useThemeColor({ light: lightColor, dark: darkColor }, "text") as string;

  return <Text style={[{ color }, type === "default" ? styles.default : undefined, type === "title" ? styles.title : undefined, type === "defaultSemiBold" ? styles.defaultSemiBold : undefined, type === "subtitle" ? styles.subtitle : undefined, type === "link" ? styles.link : undefined, style]} {...rest} />;
}

const styles = StyleSheetCreate({
  default: {
    fontSize: 16,
    lineHeight: 24,
  },
  defaultSemiBold: {
    fontSize: 16,
    lineHeight: 24,
    fontWeight: "600",
  },
  title: {
    fontSize: 32,
    fontWeight: "bold",
    lineHeight: 32,
  },
  subtitle: {
    fontSize: 20,
    fontWeight: "bold",
  },
  link: {
    lineHeight: 30,
    fontSize: 16,
    color: "#0a7ea4",
  },
});
