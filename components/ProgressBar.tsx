import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { View, ViewStyle } from "react-native";

export interface ProgressBarProps {
  progress: number; // 0-100
  height?: number;
  backgroundColor?: string;
  progressColor?: string;
  style?: ViewStyle;
}

export function ProgressBar({ progress, height = 4, backgroundColor, progressColor, style }: ProgressBarProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const clampedProgress = Math.max(0, Math.min(100, progress));

  // Use explicit color values to ensure visibility
  const bgColor = backgroundColor || colors.borderLight || '#f3f4f6';
  const fillColor = progressColor || colors.primary || '#5D8BF4';

  return (
    <View
      style={[
        {
          height,
          backgroundColor: bgColor,
          borderRadius: Math.max(2, height / 2),
          overflow: 'hidden',
          minHeight: 4,
        },
        style,
      ]}
    >
      <View
        style={{
          height: '100%',
          width: `${clampedProgress}%`,
          backgroundColor: fillColor,
          borderRadius: Math.max(2, height / 2),
        }}
      />
    </View>
  );
}


