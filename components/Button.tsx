import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import React from "react";
import { StyleProp, Text, TextStyle, TouchableOpacity, ViewStyle } from "react-native";

export interface ButtonProps {
  title: string;
  onPress: () => void;
  variant?: "primary" | "secondary" | "outline" | "ghost";
  size?: "small" | "medium" | "large";
  disabled?: boolean;
  style?: StyleProp<ViewStyle>;
  textStyle?: TextStyle;
  icon?: React.ReactNode;
  iconPosition?: "left" | "right";
}

export default function Button({ title, onPress, variant = "primary", size = "medium", disabled = false, style, textStyle, icon, iconPosition = "left" }: ButtonProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const getButtonStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      borderRadius: Layout.borderRadius.xl,
      alignItems: "center",
      justifyContent: "center",
      flexDirection: icon ? "row" : "column",
    };

    // Size styles
    switch (size) {
      case "small":
        baseStyle.height = Layout.dimensions.buttonHeightSmall;
        baseStyle.paddingHorizontal = Layout.spacing.base;
        break;
      case "large":
        baseStyle.height = Layout.dimensions.buttonHeight + 8;
        baseStyle.paddingHorizontal = Layout.spacing.xl;
        break;
      default:
        baseStyle.height = Layout.dimensions.buttonHeight;
        baseStyle.paddingHorizontal = Layout.spacing.lg;
    }

    // Variant styles
    switch (variant) {
      case "primary":
        baseStyle.backgroundColor = disabled ? colors.textMuted : colors.primary;
        break;
      case "secondary":
        baseStyle.backgroundColor = disabled ? colors.backgroundTertiary : colors.backgroundTertiary;
        break;
      case "outline":
        baseStyle.backgroundColor = "transparent";
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = disabled ? colors.textMuted : colors.primary;
        break;
      case "ghost":
        baseStyle.backgroundColor = "transparent";
        break;
    }

    return baseStyle;
  };

  const getTextStyle = (): TextStyle => {
    const baseStyle: TextStyle = {
      fontFamily: Typography.fontFamily.primary,
      fontWeight: Typography.textStyles.button.fontWeight,
    };

    // Size styles
    switch (size) {
      case "small":
        baseStyle.fontSize = Typography.textStyles.buttonSmall.fontSize;
        break;
      default:
        baseStyle.fontSize = Typography.textStyles.button.fontSize;
    }

    // Variant styles
    switch (variant) {
      case "primary":
        baseStyle.color = colors.textInverse;
        break;
      case "secondary":
        baseStyle.color = colors.text;
        break;
      case "outline":
        baseStyle.color = disabled ? colors.textMuted : colors.primary;
        break;
      case "ghost":
        baseStyle.color = disabled ? colors.textMuted : colors.primary;
        break;
    }

    if (disabled) {
      baseStyle.color = colors.textMuted;
    }

    return baseStyle;
  };

  const renderContent = () => {
    if (!icon) {
      return <Text style={[getTextStyle(), textStyle]}>{title}</Text>;
    }

    return (
      <>
        {iconPosition === "left" && (
          <React.Fragment>
            {typeof icon !== "string" && icon}
            <Text style={[getTextStyle(), textStyle, { marginLeft: Layout.spacing.sm }]}>{title}</Text>
          </React.Fragment>
        )}
        {iconPosition === "right" && (
          <React.Fragment>
            <Text style={[getTextStyle(), textStyle, { marginRight: Layout.spacing.sm }]}>{title}</Text>
            {typeof icon !== "string" && icon}
          </React.Fragment>
        )}
      </>
    );
  };

  return (
    <TouchableOpacity style={[getButtonStyle(), style]} onPress={onPress} disabled={disabled} activeOpacity={0.7}>
      {renderContent()}
    </TouchableOpacity>
  );
}
