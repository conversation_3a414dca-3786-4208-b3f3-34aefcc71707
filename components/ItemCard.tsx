import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { Pressable, Text, View, ViewStyle } from "react-native";

export interface ItemCardProps {
  item: Item;
  variant?: "default" | "compact";
  onPress?: (item: Item) => void;
  style?: ViewStyle;
}

export function ItemCard({ item, variant = "default", onPress, style }: ItemCardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const handlePress = () => {
    onPress?.(item);
  };

  return (
    <Pressable
      style={[
        styles.container,
        { backgroundColor: colors.background, shadowColor: colors.shadow },
        style,
      ]}
      onPress={handlePress}
    >
      {/* Content Image or Placeholder */}
      <View style={styles.imageContainer}>
        {item.images && item.images.length > 0 ? (
          <Image
            source={{ uri: item.images[0] }}
            style={styles.image}
            cachePolicy="memory-disk"
            recyclingKey={item.images[0]}
          />
        ) : (
          <View style={[styles.placeholderContainer, { backgroundColor: colors.backgroundTertiary }]}>
            <Ionicons name="image-outline" size={48} color={colors.textMuted} />
          </View>
        )}
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]} numberOfLines={1}>
          {item.name}
        </Text>

        {/* Item Content Preview */}
        {item.content ? (
          <Text style={[styles.description, { color: colors.textSecondary }]} numberOfLines={2}>
            {item.content}
          </Text>
        ) : (
          <Text style={[styles.description, { color: colors.textMuted }]} numberOfLines={2}>
            暂无内容
          </Text>
        )}

        {/* Location and Time Info */}
        {(item.currentLocation || item.timeOfPossession) && (
          <View style={styles.infoContainer}>
            {item.currentLocation && (
              <View style={styles.infoItem}>
                <Ionicons name="location-outline" size={12} color={colors.textMuted} />
                <Text style={[styles.infoText, { color: colors.textMuted }]} numberOfLines={1}>
                  {item.currentLocation}
                </Text>
              </View>
            )}
            {item.timeOfPossession && (
              <View style={styles.infoItem}>
                <Ionicons name="calendar-outline" size={12} color={colors.textMuted} />
                <Text style={[styles.infoText, { color: colors.textMuted }]} numberOfLines={1}>
                  {item.timeOfPossession}
                </Text>
              </View>
            )}
          </View>
        )}

        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.timeContainer}>
            <Ionicons name="time-outline" size={12} color={colors.textMuted} />
            <Text style={[styles.timeText, { color: colors.textMuted }]}>
              {new Date(item.updatedAt || new Date()).toLocaleString("zh-CN")}
            </Text>
          </View>

          {/* Tags */}
          <View style={styles.badgesContainer}>
            {item.tags?.slice(0, 3).map((tag, index) => (
              <View
                key={index}
                style={[styles.badge, { backgroundColor: colors.backgroundTertiary }]}
              >
                <Text style={[styles.badgeText, { color: colors.primary }]} numberOfLines={1}>
                  {tag}
                </Text>
              </View>
            ))}
            {item.tags && item.tags.length > 3 && (
              <View
                style={[
                  styles.badge,
                  styles.moreBadge,
                  { backgroundColor: colors.backgroundTertiary },
                ]}
              >
                <Text style={[styles.badgeText, { color: colors.textMuted }]} numberOfLines={1}>
                  +{item.tags.length - 3}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </Pressable>
  );
}

const styles = StyleSheetCreate({
  container: {
    borderRadius: 16,
    overflow: "hidden",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
    marginBottom: Layout.spacing.base,
  },
  imageContainer: {
    position: "relative",
  },
  image: {
    width: "100%",
    height: 160,
    resizeMode: "cover",
  },
  placeholderContainer: {
    width: "100%",
    height: 160,
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    padding: 12,
    gap: Layout.spacing.xs,
  },
  title: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  description: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 8,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  timeContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: 150,
  },
  timeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  // Info container styles
  infoContainer: {
    flexDirection: "row",
    gap: 8,
    marginBottom: 4,
    flexWrap: "wrap",
  },
  infoItem: {
    flexDirection: "row",
    alignItems: "center",
    gap: 2,
  },
  infoText: {
    fontSize: 10,
    maxWidth: 80,
  },
  // Tag styles
  badgesContainer: {
    flexDirection: "row",
    gap: 4,
    flexWrap: "wrap",
    flex: 1,
    justifyContent: "flex-end",
  },
  badge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    maxWidth: 60,
  },
  moreBadge: {
    minWidth: 24,
    alignItems: "center",
  },
  badgeText: {
    fontSize: 10,
    fontWeight: "500",
  },
});
