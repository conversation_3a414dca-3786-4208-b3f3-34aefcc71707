import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { useColorScheme } from "@/hooks/useColorScheme";
import { Item } from "@/types";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { Image } from "expo-image";
import { Pressable, Text, View, ViewStyle } from "react-native";

export interface ItemCardProps {
  item: Item;
  variant?: "default" | "compact";
  onPress?: (item: Item) => void;
  style?: ViewStyle;
}

export function ItemCard({ item, variant = "default", onPress, style }: ItemCardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const handlePress = () => {
    onPress?.(item);
  };

  return (
    <Pressable
      style={[
        styles.container,
        { backgroundColor: colors.background, shadowColor: colors.shadow },
        style,
      ]}
      onPress={handlePress}
    >
      {/* Cover Image */}
      <View style={styles.imageContainer}>
        <Image
          source={{ uri: item.coverImage }}
          style={styles.image}
          cachePolicy="memory-disk"
          recyclingKey={item.coverImage}
        />
        {/* {item.isLiked && (
          <View style={[styles.heartContainer, { backgroundColor: "rgba(255, 255, 255, 0.8)" }]}>
            <Ionicons name="heart.fill" size={16} color={colors.heartRed} />
          </View>
        )} */}
      </View>

      {/* Content */}
      <View style={styles.content}>
        <Text style={[styles.title, { color: colors.text }]} numberOfLines={1}>
          {item.title}
        </Text>

        {/* item Content Preview */}
        {item.mode === "timeline" ? (
          // Timeline mode: show first timeline item
          item.timelineItems && item.timelineItems.length > 0 ? (
            <View style={styles.timelinePreview}>
              <View style={styles.timelinePreviewHeader}>
                <Text
                  style={[styles.timelinePreviewTitle, { color: colors.text }]}
                  numberOfLines={1}
                >
                  {item.timelineItems[0].title}
                </Text>
              </View>
              <Text style={[styles.description, { color: colors.textSecondary }]} numberOfLines={2}>
                {item.timelineItems[0].content}
              </Text>
              {item.timelineItems.length > 1 && (
                <Text style={[styles.timelineCount, { color: colors.textMuted }]}>
                  +{item.timelineItems.length - 1} 个时间节点
                </Text>
              )}
            </View>
          ) : (
            <Text style={[styles.description, { color: colors.textMuted }]} numberOfLines={2}>
              暂无时间线内容
            </Text>
          )
        ) : item.content ? (
          // Normal mode: show regular content
          <Text style={[styles.description, { color: colors.textSecondary }]} numberOfLines={2}>
            {item.content}
          </Text>
        ) : (
          <Text style={[styles.description, { color: colors.textMuted }]} numberOfLines={2}>
            暂无内容
          </Text>
        )}

        {/* Footer */}
        <View style={styles.footer}>
          <View style={styles.timeContainer}>
            <Ionicons name="time-outline" size={12} color={colors.textMuted} />
            <Text style={[styles.timeText, { color: colors.textMuted }]}>
              {new Date(item.updatedAt || new Date()).toLocaleString("zh-CN")}
            </Text>
          </View>

          {/* Tags */}
          <View style={styles.badgesContainer}>
            {item.tags?.slice(0, 3).map((tag, index) => (
              <View
                key={index}
                style={[styles.badge, { backgroundColor: colors.backgroundTertiary }]}
              >
                <Text style={[styles.badgeText, { color: colors.primary }]} numberOfLines={1}>
                  {tag}
                </Text>
              </View>
            ))}
            {item.tags && item.tags.length > 3 && (
              <View
                style={[
                  styles.badge,
                  styles.moreBadge,
                  { backgroundColor: colors.backgroundTertiary },
                ]}
              >
                <Text style={[styles.badgeText, { color: colors.textMuted }]} numberOfLines={1}>
                  +{item.tags.length - 3}
                </Text>
              </View>
            )}
          </View>
        </View>
      </View>
    </Pressable>
  );
}

const styles = StyleSheetCreate({
  container: {
    borderRadius: 16,
    overflow: "hidden",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.1,
    shadowRadius: 6,
    elevation: 4,
    marginBottom: Layout.spacing.base,
  },
  imageContainer: {
    position: "relative",
  },
  image: {
    width: "100%",
    height: 160,
    resizeMode: "cover",
  },
  heartContainer: {
    position: "absolute",
    top: 12,
    right: 12,
    width: 32,
    height: 32,
    borderRadius: 16,
    justifyContent: "center",
    alignItems: "center",
  },
  content: {
    padding: 12,
    gap: Layout.spacing.xs,
  },
  title: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 4,
  },
  description: {
    fontSize: 12,
    lineHeight: 16,
    marginBottom: 8,
  },
  footer: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  timeContainer: {
    flexDirection: "row",
    alignItems: "center",
    width: 150,
  },
  timeText: {
    fontSize: 12,
    marginLeft: 4,
  },
  // Timeline preview styles
  timelinePreview: {
    marginBottom: 4,
  },
  timelinePreviewHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 4,
  },
  timelinePreviewTitle: {
    fontSize: 12,
    fontWeight: "500",
    flex: 1,
  },
  timelineCount: {
    fontSize: 10,
    marginTop: 2,
  },
  // Tag styles
  badgesContainer: {
    flexDirection: "row",
    gap: 4,
    flexWrap: "wrap",
    flex: 1,
    justifyContent: "flex-end",
  },
  badge: {
    paddingHorizontal: 6,
    paddingVertical: 2,
    borderRadius: 8,
    maxWidth: 60,
  },
  moreBadge: {
    minWidth: 24,
    alignItems: "center",
  },
  badgeText: {
    fontSize: 10,
    fontWeight: "500",
  },
});
