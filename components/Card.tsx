import { Colors } from '@/constants/Colors';
import { Layout } from '@/constants/Layout';
import { useColorScheme } from '@/hooks/useColorScheme';
import { StyleSheetCreate } from "@/utils";
import React from 'react';
import { View, ViewStyle } from 'react-native';

export interface CardProps {
  children: React.ReactNode;
  variant?: 'default' | 'elevated' | 'outlined';
  padding?: 'none' | 'small' | 'medium' | 'large';
  style?: ViewStyle;
  onPress?: () => void;
}

export default function Card({
  children,
  variant = 'default',
  padding = 'medium',
  style,
}: CardProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? 'light'];

  const getCardStyle = (): ViewStyle => {
    const baseStyle: ViewStyle = {
      backgroundColor: colors.background,
      borderRadius: Layout.borderRadius.xl,
      overflow: 'hidden',
    };

    // Padding styles
    switch (padding) {
      case 'none':
        break;
      case 'small':
        baseStyle.padding = Layout.spacing.sm;
        break;
      case 'large':
        baseStyle.padding = Layout.spacing.xl;
        break;
      default:
        baseStyle.padding = Layout.spacing.base;
    }

    // Variant styles
    switch (variant) {
      case 'elevated':
        Object.assign(baseStyle, Layout.shadows.md);
        break;
      case 'outlined':
        baseStyle.borderWidth = 1;
        baseStyle.borderColor = colors.border;
        break;
      default:
        Object.assign(baseStyle, Layout.shadows.sm);
    }

    return baseStyle;
  };

  return (
    <View style={[getCardStyle(), style]}>
      {children}
    </View>
  );
}

const styles = StyleSheetCreate({
  // Additional styles if needed
});
