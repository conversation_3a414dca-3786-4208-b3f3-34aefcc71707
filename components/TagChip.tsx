import { Colors } from "@/constants/Colors";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { Ionicons } from "@expo/vector-icons";
import { Pressable, Text, View, ViewStyle } from "react-native";

export interface TagChipProps {
  text: string;
  onRemove?: () => void;
  variant?: "default" | "add";
  style?: ViewStyle;
}

export function TagChip({ text, onRemove, variant = "default", style }: TagChipProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  if (variant === "add") {
    return (
      <Pressable
        style={[
          styles.container,
          styles.addContainer,
          {
            borderColor: colors.border,
          },
          style,
        ]}
      >
        <Text style={[styles.text, { color: colors.textMuted }]}>+ {text}</Text>
      </Pressable>
    );
  }

  return (
    <View
      style={[
        styles.container,
        {
          backgroundColor: colors.aiBadge,
        },
        style,
      ]}
    >
      <Text style={[styles.text, { color: colors.aiText }]}>{text}</Text>
      {onRemove && (
        <Pressable onPress={onRemove} style={styles.removeButton}>
          <Ionicons name="close" size={12} color={colors.aiText} />
        </Pressable>
      )}
    </View>
  );
}

const styles = StyleSheetCreate({
  container: {
    flexDirection: "row",
    alignItems: "center",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    marginRight: 8,
    marginBottom: 8,
  },
  addContainer: {
    borderWidth: 1,
    borderStyle: "dashed",
    backgroundColor: "transparent",
  },
  text: {
    fontSize: 12,
    fontWeight: "500",
  },
  removeButton: {
    marginLeft: 4,
    padding: 2,
  },
});
