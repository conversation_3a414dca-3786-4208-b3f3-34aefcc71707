import { Colors } from "@/constants/Colors";
import { Layout } from "@/constants/Layout";
import { Typography } from "@/constants/Typography";
import { useColorScheme } from "@/hooks/useColorScheme";
import { StyleSheetCreate } from "@/utils";
import { ScrollView, Text, TouchableOpacity, View, ViewStyle } from "react-native";

export interface Category {
  id: string;
  label: string;
}

export interface CategoryFilterProps {
  categories: Category[];
  activeCategory: string;
  onCategoryChange: (categoryId: string) => void;
  style?: ViewStyle;
}

export default function CategoryFilter({ categories, activeCategory, onCategoryChange, style }: CategoryFilterProps) {
  const colorScheme = useColorScheme();
  const colors = Colors[colorScheme ?? "light"];

  const renderCategory = (category: Category) => {
    const isActive = category.id === activeCategory;

    return (
      <TouchableOpacity
        key={category.id}
        style={[
          styles.categoryButton,
          {
            backgroundColor: isActive ? colors.primary : colors.background,
            borderColor: isActive ? colors.primary : colors.border,
          },
        ]}
        onPress={() => onCategoryChange(category.id)}
        activeOpacity={0.7}
      >
        <Text
          style={[
            styles.categoryText,
            {
              color: isActive ? colors.textInverse : colors.text,
              fontFamily: Typography.fontFamily.primary,
            },
          ]}
        >
          {category.label}
        </Text>
      </TouchableOpacity>
    );
  };

  return (
    <View style={[styles.container, style]}>
      <ScrollView horizontal showsHorizontalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {categories.map(renderCategory)}
      </ScrollView>
    </View>
  );
}

const styles = StyleSheetCreate({
  container: {
    marginVertical: Layout.spacing.sm,
  },
  scrollContent: {
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.xs,
  },
  categoryButton: {
    paddingHorizontal: Layout.spacing.base,
    paddingVertical: Layout.spacing.sm,
    borderRadius: Layout.borderRadius.full,
    borderWidth: 1,
    marginRight: Layout.spacing.sm,
    minHeight: Layout.dimensions.minTouchTarget,
    justifyContent: "center",
    alignItems: "center",
  },
  categoryText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.medium,
    textAlign: "center",
  },
});
